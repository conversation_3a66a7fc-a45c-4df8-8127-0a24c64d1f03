package logic

import (
	"context"
	"strconv"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetSmartReplyTemplate 获取商家的智能回复模板
func GetSmartReplyTemplate(ctx context.Context, merchantID string) (*model.SmartReplyTemplate, error) {
	smartReplySchema := repo.GetQuery().SmartReplyTemplate
	template, err := repo.NewSmartReplyTemplateRepo(smartReplySchema.WithContext(ctx)).SelectOne(
		search.NewQueryBuilder().
			Eq(smartReplySchema.MerchantID, merchantID).
			Build(),
	)
	if err != nil {
		return nil, err
	}
	return template, nil
}

// GetSmartReplyTemplateWithErrorHandling 获取智能回复模板并处理错误
// 返回模板、是否存在、错误
func GetSmartReplyTemplateWithErrorHandling(ctx context.Context, merchantID string) (*model.SmartReplyTemplate, bool, error) {
	template, err := GetSmartReplyTemplate(ctx, merchantID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 模板不存在
			return nil, false, nil
		}
		// 其他错误
		return nil, false, err
	}
	return template, true, nil
}

// ShouldSendSmartReplyOnConversationCreate 判断在创建会话时是否应该发送智能回复
// 规则：
// 1. 对方是商家且设置了智能回复模板并启用了智能回复功能
// 2. 发送者不是商家本人
func ShouldSendSmartReplyOnConversationCreate(ctx context.Context, senderID, receiverID string) (bool, *model.SmartReplyTemplate, error) {
	// 检查是否是商家本人（避免自己给自己回复）
	if senderID == receiverID {
		return false, nil, nil
	}

	// 获取接收者（商家）的智能回复模板
	template, err := GetSmartReplyTemplate(ctx, receiverID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 商家没有设置智能回复模板，不发送智能回复
			return false, nil, nil
		}
		log.Ctx(ctx).Errorf("获取智能回复模板失败: %v", err)
		return false, nil, err
	}

	// 检查智能回复是否启用
	if !template.IsEnabled {
		return false, nil, nil
	}

	return true, template, nil
}

// SendSmartReplyOnConversationCreate 在创建会话时发送智能回复消息
func SendSmartReplyOnConversationCreate(ctx context.Context, conversationID, clientUserID, merchantUserID string, template *model.SmartReplyTemplate) error {
	// 计算用户对的大小用户ID和消息方向（商家作为发送者）
	bigUserID, smallUserID, direction := model.CalculateUserPair(merchantUserID, clientUserID)

	// 生成消息ID
	messageID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	clientMsgID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)

	// 创建智能回复消息
	message := &model.Message{
		ID:              messageID,
		ClientMsgID:     clientMsgID,
		SmallUserID:     smallUserID,
		BigUserID:       bigUserID,
		Direction:       direction,
		MessageType:     int32(enums.MessageTypeText),
		Content:         template.TemplateContent,
		IsSmartReply:    true, // 标记为智能回复
		ClientMsgNumber: 0,    // 智能回复
		// 智能回复消息的客户端信息设置为空，因为是系统生成的
		AppChannel: "",
		AppVersion: "",
		ClientType: "",
		IP:         "",
	}

	// 使用独立事务处理智能回复发送
	return repo.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 保存智能回复消息
		if err := tx.Create(message).Error; err != nil {
			log.Ctx(ctx).Errorf("保存智能回复消息失败: %v", err)
			return err
		}

		// 更新会话的最后消息信息
		// merchantUserID=商家（智能回复的发送者），clientUserID=用户（智能回复的接收者）
		if err := UpdateConversationLastMessage(ctx, tx, merchantUserID, clientUserID,
			messageID, template.TemplateContent, enums.MessageTypeText); err != nil {
			return err
		}

		log.Ctx(ctx).Infof("创建会话时智能回复发送成功: conversationID=%s, messageID=%s, merchantID=%s", conversationID, messageID, merchantUserID)
		return nil
	})
}
