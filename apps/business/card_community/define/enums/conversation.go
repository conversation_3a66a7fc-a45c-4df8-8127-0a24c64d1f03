package enums

// ParticipantType 参与者类型
type ParticipantType int

const (
	ParticipantTypeUser     ParticipantType = 1 // 用户
	ParticipantTypeMerchant ParticipantType = 2 // 商家
)

// String 返回参与者类型的字符串表示
func (t ParticipantType) String() string {
	switch t {
	case ParticipantTypeUser:
		return "用户"
	case ParticipantTypeMerchant:
		return "商家"
	default:
		return "未知类型"
	}
}

// IsValid 检查参与者类型是否有效
func (t ParticipantType) IsValid() bool {
	switch t {
	case ParticipantTypeUser, ParticipantTypeMerchant:
		return true
	default:
		return false
	}
}

// Int32 返回参与者类型的int32值
func (t ParticipantType) Int32() int32 {
	return int32(t)
}

// DateType 日期类型
type DateType int

const (
	DateTypeCreateTime      DateType = 1 // 创建时间
	DateTypeLastMessageTime DateType = 2 // 最近消息时间
)

// IsValid
func (t DateType) IsValid() bool {
	switch t {
	case DateTypeCreateTime, DateTypeLastMessageTime:
		return true
	default:
		return false
	}
}
