package model

import (
	"time"
)

// MarkReadForUser 标记用户已读
func (c *Conversation) MarkReadForUser() {
	now := time.Now()
	c.LastMessageTime = &now
}

// UpdateLastMessage 更新最后一条消息信息
func (c *Conversation) UpdateLastMessage(messageID string, content string) {
	now := time.Now()
	c.LastMessageID = messageID
	c.LastMessageContent = content
	c.LastMessageTime = &now
}

// SetFrequencyLimited 设置频率限制状态
func (c *Conversation) SetFrequencyLimited(limited bool) {
	if limited {
		c.IsFrequencyLimited = -1 // 限制中
	} else {
		c.IsFrequencyLimited = 1 // 正常
	}
}

// IsFrequencyLimitedStatus 检查是否处于频率限制状态
func (c *Conversation) IsFrequencyLimitedStatus() bool {
	return c.IsFrequencyLimited == -1 // -1表示限制中
}
