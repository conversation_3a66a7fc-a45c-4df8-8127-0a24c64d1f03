package router

import (
	"app_service/apps/business/card_community/router/admin"
	"app_service/apps/business/card_community/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}

func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 客户端路由
	webRoute(r)
	// 管理端路由
	adminRoute(r)
}

// 客户端路由
func webRoute(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.<PERSON>(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			// 可以根据需要添加无需认证的路由
		},
	}))

	// 注册各个模块的路由
	web.Conversation(w)        // 会话管理
	web.Message(w)             // 消息管理
	web.Post(w)                // 帖子管理
	web.MerchantApplication(w) // 商家申请
	web.SmartReply(w)          // 智能回复
}

// 管理端路由
func adminRoute(router *gin.Engine) {
	a := router.Group("/admin/v1", middlewares.ParseHead)
	a.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery()) //auth.Auth(&auth.Admin{
	//	NoAuthUrl: []string{},
	//})

	// 注册各个模块的路由
	admin.Conversation(a) // 会话管理
	admin.Message(a)      // 消息管理
	admin.Post(a)         // 帖子管理
	admin.Merchant(a)     // 商家申请管理
}
