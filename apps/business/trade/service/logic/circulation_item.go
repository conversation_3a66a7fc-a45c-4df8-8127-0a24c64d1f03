package logic

import (
	"app_service/apps/business/trade/dal/model"
	"app_service/apps/business/trade/define/enums"
	"app_service/apps/business/trade/repo"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func SyncCirculationItem(ctx context.Context) error {
	logPrefix := "SyncCirculationItem"
	isHoliday, err := tmt.IsHoliday(ctx)
	if err != nil {
		return err
	}

	ciSchema := repo.GetQuery().CirculationItem
	qw := search.NewQueryBuilder().Build()
	total, err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).Count(qw)
	if err != nil {
		return err
	}
	if total == 0 {
		// 初始化
		err = InitCirculationItem(ctx)
		if err != nil {
			return err
		}
	} else {
		// 追加数据
		err = AppendCirculationItem(ctx)
		if err != nil {
			log.Ctx(ctx).Errorf("Append circulation item err:%v", err)
		}
	}

	// 批量更新
	batchSize := 20
	hasMore := true
	offset := 0
	for hasMore {
		updateQw := search.NewQueryBuilder().Eq(ciSchema.IsDisplay, enums.CirculationItemDisplayYes.Val()).Build()
		circItems, err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx).Offset(offset).Limit(batchSize)).SelectList(updateQw)
		if err != nil {
			return err
		}
		if len(circItems) == 0 {
			break
		}
		offset += batchSize
		hasMore = len(circItems) == batchSize
		var itemIDs []string
		for _, item := range circItems {
			itemIDs = append(itemIDs, item.ItemID)
		}
		issueItemMap, err := issueFacade.GetIssueItemMapByCache(ctx, itemIDs)
		if err != nil {
			return err
		}
		if isHoliday {
			// 非交易日只同步市值榜，因为上一个交易日收盘价会更新，会影响涨跌幅
			// 同步市值榜
			err = SyncMarketAmount(ctx, circItems, issueItemMap)
			if err != nil {
				log.Ctx(ctx).Errorf("%s SyncMarketAmount err:%v", logPrefix, err)
			}
			continue
		}
		// 同步热榜
		err = SyncHotList(ctx, circItems)
		if err != nil {
			log.Ctx(ctx).Errorf("%s SyncHotList err:%v", logPrefix, err)
		}
		// 同步成交额榜
		err = SyncTransactionAmount(ctx, circItems)
		if err != nil {
			log.Ctx(ctx).Errorf("%s SyncTransactionAmount err:%v", logPrefix, err)
		}
		// 同步市值榜
		err = SyncMarketAmount(ctx, circItems, issueItemMap)
		if err != nil {
			log.Ctx(ctx).Errorf("%s SyncMarketAmount err:%v", logPrefix, err)
		}
		// 同步商品信息
		err = SyncItemInfo(ctx, circItems, issueItemMap)
		if err != nil {
			log.Ctx(ctx).Errorf("%s SyncItemInfo err:%v", logPrefix, err)
		}
	}

	return nil
}

// SyncHotList 同步热门榜单
func SyncHotList(ctx context.Context, circItems []*model.CirculationItem) error {
	now := util.Now()
	startTime := util.GetStartOfDay(now)
	endTime := util.GetEndOfDay(now)
	var itemIDs []string
	circItemMap := map[string]*model.CirculationItem{}
	for _, item := range circItems {
		itemIDs = append(itemIDs, item.ItemID)
		circItemMap[item.ItemID] = item
	}
	qtyMap, err := issueFacade.GetTransactionQtyMapByItemIDs(ctx, startTime, endTime, itemIDs)
	if err != nil {
		return err
	}
	// 获取上个有交易产生的日期
	preDateMap, err := issueFacade.GetPreTransactionDateMap(ctx, itemIDs)
	if err != nil {
		return err
	}

	ciSchema := repo.GetQuery().CirculationItem
	for _, circItem := range circItems {
		itemID := circItem.ItemID
		updateParams := map[string]interface{}{
			"cur_transaction_qty": qtyMap[itemID],
		}
		if preTransDate, ok := preDateMap[itemID]; ok {
			// 获取这一天的交易数量
			st := util.GetStartOfDay(preTransDate)
			et := util.GetEndOfDay(preTransDate)
			m, err := issueFacade.GetTransactionQtyMapByItemIDs(ctx, st, et, []string{itemID})
			if err != nil {
				log.Ctx(ctx).Errorf("get transaction for item %s  error: %v", itemID, err)
			} else if qty, ok := m[itemID]; ok {
				// 以当天日期的时间戳作为权重
				updateParams["pre_transaction_qty"] = st.UnixNano() + qty
			}
		}

		updateQw := search.NewQueryBuilder().Eq(ciSchema.ID, circItem.ID).Build()
		err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
		if err != nil && !errors.Is(err, repo.UpdateFail) {
			log.Ctx(ctx).Errorf("sync hot list circulation item fail, %s", err.Error())
		}
	}

	return nil
}

// SyncTransactionAmount 同步交易额榜单
func SyncTransactionAmount(ctx context.Context, circItems []*model.CirculationItem) error {
	circItemMap := map[string]*model.CirculationItem{}
	var itemIDs []string
	for _, item := range circItems {
		itemIDs = append(itemIDs, item.ItemID)
		circItemMap[item.ItemID] = item
	}
	amountMap, err := issueFacade.GetTotalTransactionAmountMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}

	ciSchema := repo.GetQuery().CirculationItem
	for itemID, totalTransactionAmount := range amountMap {
		if totalTransactionAmount > 0 {
			circItem := circItemMap[itemID]
			if circItem == nil {
				continue
			}

			updateParams := map[string]interface{}{
				"total_transaction_amount": totalTransactionAmount,
			}
			updateQw := search.NewQueryBuilder().Eq(ciSchema.ID, circItem.ID).Build()
			err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
			if err != nil && !errors.Is(err, repo.UpdateFail) {
				log.Ctx(ctx).Errorf("sync transaction amount circulation item fail, %s", err.Error())
			}
		}
	}

	return nil
}

// SyncMarketAmount 同步市值榜（含流通数量、最新成交价、涨跌幅）
func SyncMarketAmount(ctx context.Context, circItems []*model.CirculationItem, issueItemMap map[string]*mongdb.IssueItem) error {
	logPrefix := "SyncMarketAmount"
	circItemMap := map[string]*model.CirculationItem{}
	var itemIDs []string
	for _, item := range circItems {
		itemIDs = append(itemIDs, item.ItemID)
		circItemMap[item.ItemID] = item
	}
	// 获取商品的持仓总数
	itemCountMap, err := GetTotalCirculationMapFromYc(ctx, itemIDs)
	if err != nil {
		return err
	}

	// 商品上个交易日收盘价
	preClosePriseMap, err := issueFacade.GetPreClosePriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}
	// 获取商品最新成交价
	latestSellPriceMap, err := issueFacade.GetLatestSellPriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}

	now := util.Now()
	ciSchema := repo.GetQuery().CirculationItem
	for _, circItem := range circItems {
		itemID := circItem.ItemID
		issueItem := issueItemMap[itemID]
		latestSellPrice := latestSellPriceMap[itemID]
		preClosePrice := preClosePriseMap[itemID]
		ycItemCirculationCount := itemCountMap[itemID]
		if issueItem != nil {
			// 流通数量 = 该商品首发剩余库存(首发已结束不计算)+该商品当前总用户持仓数量(排除已提货、已融合的，持仓数量每小时计算更新一次)
			totalCirculation := ycItemCirculationCount
			if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(now) {
				totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
			}
			// 计算市值
			marketAmount := totalCirculation * int64(latestSellPrice)
			// 涨跌幅 涨跌幅公式=(最新成交价-上个交易日收盘价)/上个交易日收盘价，结果四舍五入，保留(百分比)两位小数
			var priceChangeRate float32
			if latestSellPrice > 0 && preClosePrice > 0 {
				diff := decimal.NewFromInt32(latestSellPrice).Sub(decimal.NewFromInt32(preClosePrice))
				rDecimal := diff.Mul(decimal.NewFromInt32(100)).Div(decimal.NewFromInt32(preClosePrice)).Round(2)
				r, _ := rDecimal.Float64()
				priceChangeRate = float32(r)
				// 缓存涨跌幅
				err = CachePriceChangeRateForCirculationItem(ctx, itemID, r)
				if err != nil {
					log.Ctx(ctx).Errorf("%s cache price_change_rate error: %v", logPrefix, err)
				}
			}
			updateParams := map[string]interface{}{
				"market_amount":            marketAmount,           // 市值
				"total_circulation":        totalCirculation,       // 流通总数
				"price_change_rate":        priceChangeRate,        // 涨跌幅
				"latest_transaction_price": int64(latestSellPrice), // 最新成交价
			}
			updateQw := search.NewQueryBuilder().Eq(ciSchema.ID, circItem.ID).Build()
			err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
			if err != nil && !errors.Is(err, repo.UpdateFail) {
				log.Ctx(ctx).Errorf("%s update circulation item error: %v", logPrefix, err)
			}
		}
	}

	return nil
}

func SyncItemInfo(ctx context.Context, circItems []*model.CirculationItem, issueItemMap map[string]*mongdb.IssueItem) error {
	ciSchema := repo.GetQuery().CirculationItem
	for _, circItem := range circItems {
		issueItem := issueItemMap[circItem.ItemID]
		if issueItem == nil {
			continue
		}
		isDisplayVal := enums.CirculationItemDisplayYes.Val()
		isDisplay := IsDisplayToCirculationItem(issueItem)
		if !isDisplay {
			isDisplayVal = enums.CirculationItemDisplayNo.Val()
		}
		var isDelisted int32
		if IsDelisted(issueItem) {
			isDelisted = 1
		}
		updateParams := map[string]interface{}{
			"issue_item_id": issueItem.ID.Hex(),
			"item_name":     issueItem.ItemName,
			"image_url":     issueItem.ImageURL,
			"price":         int64(issueItem.Price),
			"is_display":    isDisplayVal,
			"is_delisted":   isDelisted,
		}
		updateQw := search.NewQueryBuilder().Eq(ciSchema.ID, circItem.ID).Build()
		err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
		if err != nil {
			log.Ctx(ctx).Errorf("sync transaction amount circulation circItem fail, %s", err.Error())
		}
	}

	return nil
}

func IsDisplayToCirculationItem(issueItem *mongdb.IssueItem) bool {
	// 在交易市场展示条件：
	// 1.商品开启流通
	// 2.当前时间还在流通时间段内 或者 流通结束后展示为已退市
	if issueItem != nil && issueItem.CirculationStatus == mongdb.CirculationStatusAllow &&
		issueItem.CirculationStart != nil && issueItem.CirculationEnd != nil {
		if issueItem.CirculationStart.Before(util.Now()) &&
			(issueItem.CirculationEnd.After(util.Now()) || issueItem.CirculationEndShowType == mongdb.CirculationEndShowTypeDelisted) {
			return true
		}
	}

	return false
}

// IsDelisted 是否已退市
func IsDelisted(issueItem *mongdb.IssueItem) bool {
	if issueItem != nil && issueItem.CirculationStatus == mongdb.CirculationStatusAllow &&
		issueItem.CirculationEndShowType == mongdb.CirculationEndShowTypeDelisted && issueItem.CirculationEnd != nil {
		now := util.Now()
		if now.After(*issueItem.CirculationEnd) {
			return true
		}
	}

	return false
}

func AppendCirculationItem(ctx context.Context) error {
	ciSchema := repo.GetQuery().CirculationItem
	circItems, err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx).Select(ciSchema.ItemID)).
		SelectList(search.NewQueryBuilder().Build())
	if err != nil {
		return err
	}
	var itemIDs []string
	for _, item := range circItems {
		itemIDs = append(itemIDs, item.ItemID)
	}
	issueItems, err := issueFacade.GetIssueItemsByCirculation(ctx, itemIDs)
	if err != nil {
		return err
	}

	if len(issueItems) == 0 {
		return nil
	}

	ciModels := make([]*model.CirculationItem, 0)
	for _, issueItem := range issueItems {
		if IsDisplayToCirculationItem(issueItem) {
			var isDelisted int32
			if IsDelisted(issueItem) {
				isDelisted = 1
			}
			m := &model.CirculationItem{
				ID:          snowflakeutl.GenerateID(),
				ItemID:      issueItem.ItemID.Hex(),
				IssueItemID: issueItem.ID.Hex(),
				ItemName:    issueItem.ItemName,
				ImageURL:    issueItem.ImageURL,
				Price:       int64(issueItem.Price),
				IsDisplay:   enums.CirculationItemDisplayYes.Val(),
				IsDelisted:  isDelisted,
			}
			ciModels = append(ciModels, m)
		}
	}
	if len(ciModels) > 0 {
		return repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).BatchSave(ciModels, 100)
	}

	return nil
}

func InitCirculationItem(ctx context.Context) error {
	issueItems, err := issueFacade.GetIssueItemsByCirculation(ctx, []string{})
	if err != nil {
		return err
	}
	ciModels := make([]*model.CirculationItem, 0)
	for _, issueItem := range issueItems {
		if IsDisplayToCirculationItem(issueItem) {
			var isDelisted int32
			if IsDelisted(issueItem) {
				isDelisted = 1
			}
			m := &model.CirculationItem{
				ID:          snowflakeutl.GenerateID(),
				ItemID:      issueItem.ItemID.Hex(),
				IssueItemID: issueItem.ID.Hex(),
				ItemName:    issueItem.ItemName,
				ImageURL:    issueItem.ImageURL,
				Price:       int64(issueItem.Price),
				IsDisplay:   1,
				IsDelisted:  isDelisted,
			}
			ciModels = append(ciModels, m)
		}
	}
	ciSchema := repo.GetQuery().CirculationItem
	if len(ciModels) > 0 {
		return repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).BatchSave(ciModels, 100)
	}

	return nil
}

func UpsertCirculationItem(ctx context.Context, itemID string) error {
	if itemID == "" {
		log.Ctx(ctx).Errorf("UpsertCirculationItem itemID is empty")
		return nil
	}

	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemID)
	if err != nil {
		return err
	}
	ciSchema := repo.GetQuery().CirculationItem
	qw := search.NewQueryBuilder().Eq(ciSchema.ItemID, issueItem.ItemID.Hex()).Build()
	circItem, err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).SelectOne(qw)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	isDisplay := IsDisplayToCirculationItem(issueItem)
	var isDelisted int32
	if IsDelisted(issueItem) {
		isDelisted = 1
	}
	if circItem == nil && isDisplay {
		// 新增
		m := &model.CirculationItem{
			ID:                     snowflakeutl.GenerateID(),
			ItemID:                 issueItem.ItemID.Hex(),
			IssueItemID:            issueItem.ID.Hex(),
			ItemName:               issueItem.ItemName,
			ImageURL:               issueItem.ImageURL,
			Price:                  int64(issueItem.Price),
			LatestTransactionPrice: int64(issueItem.Price), // 最新成交价初始化为发行价格
			IsDisplay:              enums.CirculationItemDisplayYes.Val(),
			IsDelisted:             isDelisted,
		}
		return repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).Save(m)
	} else if circItem != nil {
		// 更新
		isDisplayVal := enums.CirculationItemDisplayYes.Val()
		if !isDisplay {
			isDisplayVal = enums.CirculationItemDisplayNo.Val()
		}
		updateItem := model.CirculationItem{
			ID:          circItem.ID,
			IssueItemID: issueItem.ID.Hex(),
			ItemName:    issueItem.ItemName,
			ImageURL:    issueItem.ImageURL,
			Price:       int64(issueItem.Price),
			IsDisplay:   isDisplayVal,
			IsDelisted:  isDelisted,
		}
		return repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).UpdateById(&updateItem)
	}

	return nil
}

func GetPriceChangeRateMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]float32, error) {
	// 先从缓存获取
	resultMap := map[string]float32{}
	var noCacheItemIDs []string
	for _, itemID := range itemIDs {
		score, err := GetPriceChangeRateFromCache(ctx, itemID)
		if err == nil {
			resultMap[itemID] = float32(score)
		} else {
			// 无缓存
			noCacheItemIDs = append(noCacheItemIDs, itemID)
		}
	}

	if len(noCacheItemIDs) > 0 {
		// 从数据库取
		ciSchema := repo.GetQuery().CirculationItem
		qw := search.NewQueryBuilder().
			Select(ciSchema.ItemID, ciSchema.PriceChangeRate).
			In(ciSchema.ItemID, noCacheItemIDs).
			Build()
		circItems, err := repo.NewCirculationItemRepo(ciSchema.WithContext(ctx)).SelectList(qw)
		if err != nil {
			return nil, err
		}

		for _, circItem := range circItems {
			resultMap[circItem.ItemID] = circItem.PriceChangeRate
		}
	}

	return resultMap, nil
}
