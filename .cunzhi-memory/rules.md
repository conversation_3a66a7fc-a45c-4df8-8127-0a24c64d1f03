# 开发规范和规则

- 用户端分页使用HasMore格式，管理端分页参考其他代码的标准格式
- 会话表设计规则：一条会话记录两条数据，支持已读、删除联系人功能；索引性能考虑用户查询联系人时sql条件为where uid_a=?，联系人表索引为uid_a；消息表一条消息记录一条数据，使用<big_uid, small_uid>联合索引，查询sql为where big_uid=max(uid_a,uid_b) and small_uid=min(uid_a,uid_b)，根据direction字段展示聊天方向
- 管理端新增功能需求：1.求购管理模块添加违规下架API接口(status=-3)；2.消息管理模块将消息接口从会话详情中分离，提供独立的分页查询消息列表API
- 用户反馈：消息表设计需要参考转转IM架构，采用<big_uid, small_uid>联合索引设计，一条消息记录一条数据，通过direction字段展示聊天方向，节省一半存储空间。查询SQL统一为：where big_uid=max(uid_a,uid_b) and small_uid=min(uid_a,uid_b)
- 帖子消息设计规则：聊天里的帖子信息是快照形式，直接使用MessageType=3(原视频类型)作为帖子消息类型，当前版本不支持视频功能
- 项目规范：价格应使用"分"作为单位的整数(bigint)存储，避免浮点数精度问题
- 帖子业务规则更新：1.价格验证移除10的倍数检查，前端直接传分为单位的整数；2.用户不能将帖子状态设为违规下架(-3)；3.已删除(-1)和违规下架(-3)的帖子不能变更状态
- 消息表字段修改：将logical_timestamp字段改名为client_number，注释改为"客户端生成序号，用于消息排序"，需要同步更新数据库迁移脚本、GORM模型和相关业务逻辑
- 消息表字段名修正：将client_number改为client_msg_number，注释为"客户端生成序号，用于消息排序"，需要同步更新数据库迁移脚本、GORM模型和相关业务逻辑
- 消息发送频率限制规则：用户向某个接收方连续发送超过5条消息且对方未回复时，提示"你已向 Ta 发送 5 条消息，请耐心等待回复～"并阻止发送；对方回复后重置计数；不考虑时间窗口和消息类型- 消息发送频率限制实现方案：会话表增加is_frequency_limited标记字段，发送消息时判断标记，有标记则查询消息数量判断是否拦截，对方回复时清除标记，避免每次都查询数据库提高性能
