definitions:
  app_service_apps_business_announcement_define.GetItemIdInfoList:
    properties:
      id:
        description: ID
        type: string
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
    type: object
  app_service_apps_business_announcement_define.NoticeAggregateResp:
    properties:
      list:
        items:
          $ref: '#/definitions/app_service_apps_business_announcement_define.UnifiedNotice'
        type: array
    type: object
  app_service_apps_business_announcement_define.SchedulePublishReq:
    type: object
  app_service_apps_business_announcement_define.SchedulePublishResp:
    type: object
  app_service_apps_business_announcement_define.UnifiedNotice:
    properties:
      id:
        type: string
      link:
        type: string
      sort_time:
        type: string
      title:
        type: string
    type: object
  app_service_apps_business_market_changes_define.GetCategoryAdminLessDetailResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  app_service_apps_business_market_changes_define.SchedulePublishReq:
    type: object
  app_service_apps_business_market_changes_define.SchedulePublishResp:
    type: object
  app_service_apps_business_operation_announcement_define.GetItemIdInfoList:
    properties:
      id:
        description: ID
        type: string
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
    type: object
  app_service_apps_business_operation_announcement_define.SchedulePublishReq:
    type: object
  app_service_apps_business_operation_announcement_define.SchedulePublishResp:
    type: object
  app_service_apps_business_yc_define.ForcedShipmentBatchAdminReq:
    properties:
      item_id:
        type: string
    required:
    - item_id
    type: object
  app_service_apps_business_yc_define.ForcedShipmentBatchAdminResp:
    properties:
      list:
        additionalProperties:
          items:
            $ref: '#/definitions/yc_open.GetUserItemsByItemIdListResItem'
          type: array
        type: object
      total:
        type: integer
    type: object
  define.ActivityWebInfo:
    properties:
      id:
        example: "0"
        type: string
      is_can_jump:
        description: 是否可跳转
        type: boolean
      items:
        description: 商品列表（仅行情异动）
        items:
          $ref: '#/definitions/define.ActivityWebItem'
        type: array
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 标题
        type: string
      type:
        description: '类型，platform_announcement: 平台方公告，operation_announcement: 运营方公告，market_changes:
          行情异动'
        type: string
    type: object
  define.ActivityWebItem:
    properties:
      circulation_status:
        allOf:
        - $ref: '#/definitions/mongdb.CirculationStatusEnum'
        description: 流通状态
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      price_changes:
        description: 价格变动
        type: number
      status:
        allOf:
        - $ref: '#/definitions/mongdb.IssueItemStatusEnum'
        description: 状态
    type: object
  define.AddAnnCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - name
    - priority
    - text_color
    type: object
  define.AddAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.AddAnnouncementReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 公告内容
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
        enum:
        - 1
        - 2
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementPublishType'
        description: 发布类型
      title:
        description: 公告标题
        type: string
    required:
    - category_id
    - channel_ids
    - content
    - message_push
    - publish_type
    - title
    type: object
  define.AddAnnouncementResp:
    properties:
      id:
        description: 公告ID
        type: integer
    type: object
  define.AddBonusItemReq:
    properties:
      exchange_end_time:
        description: 兑换结束时间（RFC3339格式）
        type: string
      exchange_price:
        description: 兑换价格
        type: integer
      exchange_start_time:
        description: 兑换开始时间（RFC3339格式）
        type: string
      exchange_user_type:
        description: 可兑人群类型（-1表示不限）
        enum:
        - -1
        type: integer
      per_user_limit_qty:
        description: 用户限兑数量（-1表示不限量）
        type: integer
      per_user_limit_refresh_rate:
        description: 限兑刷新频率，not_refresh：不刷新，1d：每天刷新
        enum:
        - not_refresh
        - 1d
        type: string
      priority:
        description: 优先级数值，0-9999
        maximum: 9999
        minimum: 0
        type: integer
      steam_item_id:
        description: 实物商品ID
        type: string
      stock_qty:
        description: 库存量
        minimum: 1
        type: integer
    required:
    - exchange_end_time
    - exchange_price
    - exchange_start_time
    - exchange_user_type
    - per_user_limit_qty
    - per_user_limit_refresh_rate
    - priority
    - steam_item_id
    - stock_qty
    type: object
  define.AddBonusItemResp:
    properties:
      id:
        example: "0"
        type: string
    type: object
  define.AddCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      relate_type:
        allOf:
        - $ref: '#/definitions/enum.CategoryRelateType'
        description: 关联类型
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - name
    - priority
    - relate_type
    - text_color
    type: object
  define.AddCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.AddMarketChangesReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 行情异动内容
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesMessagePushType'
        description: 消息推送
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesPublishType'
        description: 发布类型
      title:
        description: 行情异动标题
        type: string
    required:
    - category_id
    - channel_ids
    - publish_type
    - title
    type: object
  define.AddMarketChangesResp:
    properties:
      id:
        description: 行情异动ID
        example: "0"
        type: string
    type: object
  define.AddOperationAnnCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - name
    - priority
    - text_color
    type: object
  define.AddOperationAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.AddOperationAnnouncementReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 运营公告内容
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
        enum:
        - 1
        - 2
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementPublishType'
        description: 发布类型
      title:
        description: 运营公告标题
        type: string
    required:
    - category_id
    - channel_ids
    - content
    - message_push
    - publish_type
    - title
    type: object
  define.AddOperationAnnouncementResp:
    properties:
      id:
        description: 运营公告ID
        type: integer
    type: object
  define.AddStoryReq:
    properties:
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      add_story_materials:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsAddData'
        type: array
      cover_url:
        description: 封面图
        type: string
      end_time:
        description: 结束时间
        type: string
      item_id:
        description: 故事玩法物品id(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      release_data:
        allOf:
        - $ref: '#/definitions/define.StoryReleaseTime'
        description: 故事玩法材料释放时间
      scene_id:
        description: 故事玩法场景id
        type: integer
      start_time:
        description: 开始时间
        type: string
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    required:
    - activity_desc
    - activity_type
    - add_story_materials
    - cover_url
    - end_time
    - item_id
    - item_title
    - scene_id
    - start_time
    - stock_display
    - title
    - total_stock
    - user_limit
    type: object
  define.AddStoryResp:
    properties:
      id:
        description: 故事玩法ID
        type: integer
    type: object
  define.AddStorySceneReq:
    properties:
      content:
        description: 场景介绍
        type: string
      cover_url:
        description: 封面图
        type: string
      name:
        description: 场景名称
        type: string
    required:
    - content
    - cover_url
    - name
    type: object
  define.AddStorySceneResp:
    properties:
      id:
        description: 场景ID
        type: integer
    type: object
  define.AddSynthesisReq:
    properties:
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      add_synthesis_materials:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsAddData'
        type: array
      cover_url:
        description: 封面图
        type: string
      end_time:
        description: 结束时间
        type: string
      item_id:
        description: 合成物品id(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      release_data:
        allOf:
        - $ref: '#/definitions/define.SynthesisReleaseTime'
        description: 融合材料释放时间
      start_time:
        description: 开始时间
        type: string
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    required:
    - activity_desc
    - activity_type
    - add_synthesis_materials
    - cover_url
    - end_time
    - item_id
    - item_title
    - start_time
    - stock_display
    - title
    - total_stock
    - user_limit
    type: object
  define.AddSynthesisResp:
    properties:
      id:
        description: 合成活动ID
        type: integer
    type: object
  define.Banner:
    properties:
      img_url:
        type: string
      link:
        type: string
    type: object
  define.BatchUpdateCirculationItemResp:
    type: object
  define.BindReq:
    properties:
      invite_code:
        type: string
    type: object
  define.BindResp:
    type: object
  define.BonusExpireResp:
    type: object
  define.BonusItemListAdminInfo:
    properties:
      exchange_end_time:
        description: 兑换结束时间（RFC3339格式）
        type: string
      exchange_price:
        description: 兑换价格
        type: integer
      exchange_start_time:
        description: 兑换开始时间（RFC3339格式）
        type: string
      exchanged_qty:
        description: 已兑换数量
        type: integer
      icon_url:
        description: 商品主图
        type: string
      id:
        example: "0"
        type: string
      item_name:
        description: 商品名称
        type: string
      per_user_limit_qty:
        description: 用户限兑数量（-1表示不限量）
        type: integer
      priority:
        description: 优先级
        type: integer
      sku_no:
        description: 商品 sku
        type: string
      status:
        description: 状态，0：待上架，1：已上架，2：已下架，3：已结束
        type: integer
      steam_item_id:
        description: 实物商品ID
        type: string
      stock_qty:
        description: 库存量
        type: integer
    type: object
  define.CheckAndAlarmAbnormalExchangeLogReq:
    type: object
  define.CheckAndAlarmAbnormalExchangeLogResp:
    properties:
      abnormal_exchange_log_ids:
        description: 异常兑换记录列表
        items:
          type: integer
        type: array
      total:
        description: 异常兑换记录总数
        type: integer
    type: object
  define.CirculationItemWebListInfo:
    properties:
      id:
        example: "0"
        type: string
      image_url:
        type: string
      is_delisted:
        description: 是否退市，true：已退市
        type: boolean
      item_id:
        type: string
      item_name:
        type: string
      latest_transaction_price:
        description: 最新成交价（单位：分）
        type: integer
      market_amount:
        description: 市值（单位：分）
        type: integer
      price_change_rate:
        description: 涨跌幅
        type: number
      total_circulation:
        description: 流通数量
        type: integer
      transaction_amount:
        description: 成交额（单位：分）
        type: integer
    type: object
  define.CirculationItemWebTopListInfo:
    properties:
      id:
        example: "0"
        type: string
      image_url:
        type: string
      item_id:
        type: string
      item_name:
        type: string
      price_change_rate:
        description: 涨跌幅
        type: number
    type: object
  define.ClearExpiredExchangeLimitCacheReq:
    type: object
  define.ClearExpiredExchangeLimitCacheResp:
    type: object
  define.CreateConversationReq:
    properties:
      other_participant_id:
        description: 对方ID（用户ID或商家ID）
        type: string
    required:
    - other_participant_id
    type: object
  define.CreateConversationResp:
    properties:
      id:
        description: 会话记录ID
        type: string
      is_new:
        description: 是否新创建
        type: boolean
    type: object
  define.CreatePostReq:
    properties:
      description:
        description: 帖子描述
        maxLength: 800
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        maxItems: 12
        minItems: 1
        type: array
      price:
        description: 收购价格（分为单位，最多一位小数即10的倍数）
        maximum: 99990
        minimum: 0
        type: integer
    required:
    - description
    - media_files
    - price
    type: object
  define.CreatePostResp:
    properties:
      id:
        description: 帖子ID
        type: string
    type: object
  define.DelAnnCategoryReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.DelAnnouncementReq:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelAnnouncementResp:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    type: object
  define.DelCategoryReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.DelOperationAnnCategoryReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelOperationAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.DelOperationAnnouncementReq:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelOperationAnnouncementResp:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    type: object
  define.DelStoryReq:
    properties:
      id:
        description: 故事玩法ID
        type: integer
    type: object
  define.DelStoryResp:
    type: object
  define.DelSynthesisReq:
    properties:
      id:
        description: 合成活动ID
        type: integer
    type: object
  define.DelSynthesisResp:
    type: object
  define.DeletePostReq:
    properties:
      id:
        description: 帖子ID
        type: string
    required:
    - id
    type: object
  define.DeletePostResp:
    properties:
      id:
        description: 帖子ID
        type: string
    type: object
  define.EditAnnCategoryPriorityReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      priority:
        description: 优先级
        type: integer
    required:
    - id
    - priority
    type: object
  define.EditAnnCategoryPriorityResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditAnnCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - id
    - name
    - priority
    - text_color
    type: object
  define.EditAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditAnnouncementAdIdsReq:
    properties:
      ad_ids:
        description: 关联广告ID列表
        items:
          type: string
        type: array
      id:
        description: 公告ID
        example: "0"
        type: string
    required:
    - ad_ids
    - id
    type: object
  define.EditAnnouncementAdIdsResp:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    type: object
  define.EditAnnouncementPriorityReq:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
      priority:
        description: 优先级
        type: integer
    required:
    - id
    - priority
    type: object
  define.EditAnnouncementPriorityResp:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    type: object
  define.EditAnnouncementReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 公告内容
        type: string
      id:
        description: 公告ID
        example: "0"
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
        enum:
        - 1
        - 2
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementPublishType'
        description: 发布类型
      title:
        description: 公告标题
        type: string
    required:
    - category_id
    - channel_ids
    - content
    - id
    - message_push
    - publish_type
    - title
    type: object
  define.EditAnnouncementResp:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    type: object
  define.EditAnnouncementStatusReq:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementStatus'
        description: 状态
    required:
    - id
    - status
    type: object
  define.EditAnnouncementStatusResp:
    properties:
      id:
        description: 公告ID
        example: "0"
        type: string
    type: object
  define.EditBonusItemReq:
    properties:
      exchange_end_time:
        description: 兑换结束时间（RFC3339格式）
        type: string
      exchange_start_time:
        description: 兑换开始时间（RFC3339格式）
        type: string
      exchange_user_type:
        description: 可兑人群类型，-1：不限
        enum:
        - -1
        type: integer
      id:
        example: "0"
        type: string
      per_user_limit_qty:
        description: 用户限兑数量（-1表示不限量）
        type: integer
      priority:
        description: 优先级数值，0-9999
        maximum: 9999
        minimum: 0
        type: integer
      stock_qty:
        description: 库存量
        minimum: 1
        type: integer
    required:
    - exchange_end_time
    - exchange_start_time
    - exchange_user_type
    - id
    - per_user_limit_qty
    - priority
    - stock_qty
    type: object
  define.EditBonusItemResp:
    properties:
      id:
        example: "0"
        type: string
    type: object
  define.EditBonusItemStatusReq:
    properties:
      id:
        example: "0"
        type: string
      status:
        description: 状态，1：已上架，2：已下架
        enum:
        - 1
        - 2
        type: integer
    required:
    - id
    - status
    type: object
  define.EditBonusItemStatusResp:
    properties:
      id:
        example: "0"
        type: string
    type: object
  define.EditCategoryPriorityReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      priority:
        description: 优先级
        type: integer
    required:
    - id
    - priority
    type: object
  define.EditCategoryPriorityResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - id
    - name
    - priority
    - text_color
    type: object
  define.EditCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditCategoryStatusReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enum.CategoryStatus'
        description: 状态
    required:
    - id
    - status
    type: object
  define.EditCategoryStatusResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditInviteConfigAdminReq:
    properties:
      invite_reward_config:
        $ref: '#/definitions/define.InviteRewardConfig'
    type: object
  define.EditInviteConfigAdminResp:
    type: object
  define.EditMarketChangesReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 行情异动内容
        type: string
      id:
        description: 行情异动ID
        example: "0"
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesMessagePushType'
        description: 消息推送
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesPublishType'
        description: 发布类型
      title:
        description: 行情异动标题
        type: string
    required:
    - category_id
    - channel_ids
    - id
    - publish_type
    - title
    type: object
  define.EditMarketChangesResp:
    properties:
      id:
        description: 行情异动ID
        example: "0"
        type: string
    type: object
  define.EditMarketChangesStatusReq:
    properties:
      id:
        description: 行情异动ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesStatus'
        description: 状态
    required:
    - id
    - status
    type: object
  define.EditMarketChangesStatusResp:
    properties:
      id:
        description: 行情异动ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnCategoryPriorityReq:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      priority:
        description: 优先级
        type: integer
    required:
    - id
    - priority
    type: object
  define.EditOperationAnnCategoryPriorityResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnCategoryReq:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      text_color:
        description: 文字颜色
        type: string
    required:
    - background_color
    - id
    - name
    - priority
    - text_color
    type: object
  define.EditOperationAnnCategoryResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnouncementAdIdsReq:
    properties:
      ad_ids:
        description: 关联广告ID列表
        items:
          type: string
        type: array
      id:
        description: 运营公告ID
        example: "0"
        type: string
    required:
    - ad_ids
    - id
    type: object
  define.EditOperationAnnouncementAdIdsResp:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnouncementPriorityReq:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
      priority:
        description: 优先级
        type: integer
    required:
    - id
    - priority
    type: object
  define.EditOperationAnnouncementPriorityResp:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnouncementReq:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 运营公告内容
        type: string
      id:
        description: 运营公告ID
        example: "0"
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
        enum:
        - 1
        - 2
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementPublishType'
        description: 发布类型
      title:
        description: 运营公告标题
        type: string
    required:
    - category_id
    - channel_ids
    - content
    - id
    - message_push
    - publish_type
    - title
    type: object
  define.EditOperationAnnouncementResp:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    type: object
  define.EditOperationAnnouncementStatusReq:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementStatus'
        description: 状态
    required:
    - id
    - status
    type: object
  define.EditOperationAnnouncementStatusResp:
    properties:
      id:
        description: 运营公告ID
        example: "0"
        type: string
    type: object
  define.EditPostReq:
    properties:
      description:
        description: 帖子描述
        maxLength: 800
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        maxItems: 12
        minItems: 1
        type: array
      price:
        description: 收购价格（分为单位，最多一位小数即10的倍数）
        maximum: 99990
        minimum: 0
        type: integer
    required:
    - description
    - id
    - media_files
    - price
    type: object
  define.EditPostResp:
    properties:
      id:
        description: 帖子ID
        type: string
    type: object
  define.EditShipManageReq:
    properties:
      admin_remark:
        description: 管理员备注
        type: string
      extends:
        allOf:
        - $ref: '#/definitions/yc_open.UpdateExtends'
        description: 扩展信息
      operator:
        allOf:
        - $ref: '#/definitions/pat.CheckAdmJwtUserInfo'
        description: 操作员信息
      status:
        allOf:
        - $ref: '#/definitions/define.ItemWithdrawOrderStatus'
        description: 订单状态
      yc_id:
        description: 云仓ID
        type: string
    required:
    - yc_id
    type: object
  define.EditShipManageResp:
    properties:
      yc_id:
        description: 云仓ID
        type: string
    type: object
  define.EditStoryReq:
    properties:
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      edit_story_materials:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsEditData'
        type: array
      end_time:
        description: 结束时间
        type: string
      id:
        description: 故事玩法ID
        type: integer
      item_id:
        description: 故事玩法物品id(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      release_data:
        allOf:
        - $ref: '#/definitions/define.StoryReleaseTime'
        description: 故事玩法材料释放时间
      scene_id:
        description: 故事玩法场景id
        type: integer
      start_time:
        description: 开始时间
        type: string
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    required:
    - activity_desc
    - activity_type
    - cover_url
    - edit_story_materials
    - end_time
    - id
    - item_id
    - item_title
    - scene_id
    - start_time
    - stock_display
    - title
    - total_stock
    - user_limit
    type: object
  define.EditStoryResp:
    properties:
      id:
        description: 故事玩法ID
        type: integer
    type: object
  define.EditStoryRuleReq:
    properties:
      content:
        type: string
    required:
    - content
    type: object
  define.EditStoryRuleResp:
    type: object
  define.EditStorySceneReq:
    properties:
      content:
        description: 场景介绍
        type: string
      cover_url:
        description: 封面图
        type: string
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
    required:
    - content
    - cover_url
    - id
    - name
    type: object
  define.EditStorySceneResp:
    properties:
      id:
        description: 场景ID
        type: integer
    type: object
  define.EditStoryStatusReq:
    properties:
      id:
        description: 故事玩法ID
        type: integer
      status:
        description: 状态 2:上架;3:下架
        type: integer
    required:
    - status
    type: object
  define.EditStoryStatusResp:
    type: object
  define.EditSynthesisReq:
    properties:
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      edit_synthesis_materials:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsEditData'
        type: array
      end_time:
        description: 结束时间
        type: string
      id:
        description: 合成活动ID
        type: integer
      item_id:
        description: 合成物品id(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      release_data:
        allOf:
        - $ref: '#/definitions/define.SynthesisReleaseTime'
        description: 融合材料释放时间
      start_time:
        description: 开始时间
        type: string
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    required:
    - activity_desc
    - activity_type
    - cover_url
    - edit_synthesis_materials
    - end_time
    - item_id
    - item_title
    - start_time
    - stock_display
    - title
    - total_stock
    - user_limit
    type: object
  define.EditSynthesisResp:
    properties:
      id:
        description: 合成活动ID
        type: integer
    type: object
  define.EditSynthesisRuleReq:
    properties:
      content:
        type: string
    required:
    - content
    type: object
  define.EditSynthesisRuleResp:
    type: object
  define.EditSynthesisStatusReq:
    properties:
      id:
        description: 合成活动ID
        type: integer
      status:
        description: 状态 2:上架;3:下架
        type: integer
    required:
    - status
    type: object
  define.EditSynthesisStatusResp:
    type: object
  define.ExchangeLogAdminInfo:
    properties:
      bonus_item_id:
        description: 积分商品 id/活动 id
        example: "0"
        type: string
      bonus_total:
        description: 消耗积分
        type: integer
      cost_total:
        description: 成本合计（单位：分）
        type: integer
      exchange_price:
        description: 兑换价格（积分）
        type: integer
      exchange_qty:
        description: 兑换数量
        type: integer
      exchange_time:
        description: 兑换时间
        type: string
      icon_url:
        description: 商品主图
        type: string
      id:
        example: "0"
        type: string
      item_name:
        description: 商品名称
        type: string
      mobile_phone:
        description: 用户手机号
        type: string
      nick_name:
        description: 用户昵称
        type: string
      sku_no:
        description: 商品 sku
        type: string
      status:
        description: 状态，0：处理中，1：成功，2：失败
        type: integer
      steam_item_id:
        description: 实物商品 id
        type: string
      user_id:
        description: 用户 id
        type: string
      yc_withdraw_order_id:
        description: 云仓发货单号
        type: string
    type: object
  define.FinishBonusItemReq:
    type: object
  define.FinishBonusItemResp:
    type: object
  define.FlowStatusEnum:
    enum:
    - 1
    - 2
    - 4
    - 5
    - 6
    - 3001
    - 3002
    - 3003
    - 3004
    - 3005
    format: int32
    type: integer
    x-enum-comments:
      FlowStatusAvailable: 可流转
      FlowStatusCountdown: 可售倒计时
      FlowStatusForbidden: 禁止流转
      FlowStatusFused: 已融合
      FlowStatusOrderClose: 已取消
      FlowStatusOrderOff: 已下架
      FlowStatusOrderSell: 出售中
      FlowStatusOrderSellOut: 已出售
      FlowStatusOrderSelling: 交易中
      FlowStatusStory: 已探索
    x-enum-descriptions:
    - 可流转
    - 禁止流转
    - 已融合
    - 可售倒计时
    - 已探索
    - 出售中
    - 已取消
    - 已出售
    - 已下架
    - 交易中
    x-enum-varnames:
    - FlowStatusAvailable
    - FlowStatusForbidden
    - FlowStatusFused
    - FlowStatusCountdown
    - FlowStatusStory
    - FlowStatusOrderSell
    - FlowStatusOrderClose
    - FlowStatusOrderSellOut
    - FlowStatusOrderOff
    - FlowStatusOrderSelling
  define.Freight:
    properties:
      com:
        description: 快递公司
        type: string
      freight_no:
        description: 运单号
        type: string
      is_check:
        description: 运单号
        type: boolean
      records:
        description: 快递公司
        items:
          $ref: '#/definitions/define.Record'
        type: array
      user_id:
        description: |-
          状态
          Status FreightStatusEnum,
          用户ID
        type: string
      withdraw_order_id:
        description: 订单ID
        type: string
    type: object
  define.FreightSyncReq:
    properties:
      freight_nos:
        description: 快递单号列表
        items:
          type: string
        type: array
    type: object
  define.FreightSyncResp:
    type: object
  define.GenerateIDResp:
    properties:
      id:
        description: 生成的Snowflake ID
        type: integer
    type: object
  define.GetAnnCategoryAdminDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.AnnCategoryStatus'
        description: 状态
      text_color:
        description: 文字颜色
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetAnnCategoryAdminLessDetailResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  define.GetAnnCategoryAdminListData:
    properties:
      ann_num:
        description: 公告数量
        type: integer
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.AnnCategoryStatus'
        description: 状态
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetAnnCategoryAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAnnCategoryAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetAnnCategoryWebLessDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      text_color:
        description: 文字颜色
        type: string
    type: object
  define.GetAnnCategoryWebListData:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  define.GetAnnCategoryWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetAnnCategoryWebListData'
        type: array
    type: object
  define.GetAnnouncementAdminDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetAnnCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 公告内容
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 公告ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/app_service_apps_business_announcement_define.GetItemIdInfoList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementStatus'
        description: 状态
      title:
        description: 公告标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetAnnouncementAdminListData:
    properties:
      ad_ids:
        description: CreatorInfo   *GetUserInfoAdminResp             `json:"creator_info"`       //
          创建人信息
        items:
          type: string
        type: array
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetAnnCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 公告内容
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 公告ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/app_service_apps_business_announcement_define.GetItemIdInfoList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.AnnouncementStatus'
        description: 状态
      title:
        description: 公告标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetAnnouncementAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAnnouncementAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetAnnouncementWebDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetAnnCategoryWebLessDetailResp'
        description: 分类信息
      content:
        description: 公告内容
        type: string
      id:
        description: 公告ID
        example: "0"
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 公告标题
        type: string
    type: object
  define.GetAnnouncementWebListData:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetAnnCategoryWebLessDetailResp'
        description: 分类信息
      current_time:
        description: 当前时间
        type: string
      id:
        description: 公告ID
        example: "0"
        type: string
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 公告标题
        type: string
    type: object
  define.GetAnnouncementWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetAnnouncementWebListData'
        type: array
    type: object
  define.GetBonusItemDetailAdminReq:
    properties:
      id:
        example: "0"
        type: string
    required:
    - id
    type: object
  define.GetBonusItemDetailAdminResp:
    properties:
      exchange_end_time:
        description: 兑换结束时间（RFC3339格式）
        type: string
      exchange_price:
        description: 兑换价格
        type: integer
      exchange_start_time:
        description: 兑换开始时间（RFC3339格式）
        type: string
      exchange_user_type:
        description: 可兑人群类型
        type: integer
      id:
        example: "0"
        type: string
      per_user_limit_qty:
        description: 用户限兑数量（-1表示不限量）
        type: integer
      per_user_limit_refresh_rate:
        description: 限兑刷新频率枚举值
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        description: 状态，0：待上架，1：已上架，2：已下架，3：已结束
        type: integer
      steam_item_id:
        description: 实物商品ID
        type: string
      stock_qty:
        description: 库存量
        type: integer
    type: object
  define.GetBonusItemListAdminReq:
    properties:
      end_time:
        type: string
      id:
        description: 积分商品 id/活动 id
        example: "0"
        type: string
      item_keyword:
        description: 商品搜索关键词
        type: string
      page:
        description: 页码
        type: integer
      page_size:
        description: 每页显示的条目数量
        type: integer
      sku_no:
        description: 商品 sku
        type: string
      start_time:
        type: string
      status:
        description: 状态，0：待上架，1：已上架，2：已下架，3：已结束
        example: "0"
        type: string
    required:
    - page
    - page_size
    type: object
  define.GetBonusItemListAdminResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.BonusItemListAdminInfo'
        type: array
      total:
        type: integer
    type: object
  define.GetBonusItemWebDetailReq:
    properties:
      id:
        example: "0"
        type: string
    required:
    - id
    type: object
  define.GetBonusItemWebDetailResp:
    properties:
      current_time:
        description: 服务器当前时间
        type: string
      detail_h5:
        description: 客户端详情
        type: string
      exchange_end_time:
        description: 兑换结束时间（RFC3339格式）
        type: string
      exchange_price:
        description: 兑换价格（积分）
        type: integer
      exchange_start_time:
        description: 兑换开始时间（RFC3339格式）
        type: string
      icon_url:
        description: 主图
        type: string
      id:
        example: "0"
        type: string
      image_infos:
        description: 展示图
        items:
          type: string
        type: array
      item_name:
        description: 商品名称
        type: string
      per_user_limit_qty:
        description: 用户限兑数量（-1表示不限量）
        type: integer
      per_user_limit_refresh_rate:
        description: 限兑刷新频率枚举值
        type: string
      sell_price:
        description: 出售价格（单位：分）
        type: integer
      status:
        description: 状态，0：待上架，1：已上架，2：已下架，3：已结束
        type: integer
      stock_qty:
        description: 库存量
        type: integer
    type: object
  define.GetBonusItemWebListInfo:
    properties:
      current_time:
        description: 系统当前时间
        type: string
      exchange_end_time:
        description: 兑换结束时间
        type: string
      exchange_price:
        description: 兑换价格（积分）
        type: integer
      exchange_start_time:
        description: 兑换开始时间
        type: string
      icon_url:
        description: 商品主图
        type: string
      id:
        example: "0"
        type: string
      item_name:
        description: 商品名称
        type: string
      sell_price:
        description: 售价（单位：分）
        type: integer
      status:
        description: 状态，0：待上架，1：已上架，2：已下架，3：已结束
        type: integer
      stock_qty:
        description: 库存
        type: integer
    type: object
  define.GetBonusItemWebListReq:
    properties:
      page:
        description: 页码
        type: integer
      page_size:
        description: 每页显示的条目数量
        type: integer
    required:
    - page
    - page_size
    type: object
  define.GetBonusItemWebListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetBonusItemWebListInfo'
        type: array
      total:
        type: integer
    type: object
  define.GetCategoryAdminDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enum.CategoryStatus'
        description: 状态
      text_color:
        description: 文字颜色
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetCategoryAdminListData:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      num:
        description: 数量
        type: integer
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enum.CategoryStatus'
        description: 状态
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetCategoryAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetCategoryAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetCategoryWebLessDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      text_color:
        description: 文字颜色
        type: string
    type: object
  define.GetCategoryWebListData:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  define.GetCategoryWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetCategoryWebListData'
        type: array
    type: object
  define.GetCirculationItemWebListResp:
    properties:
      has_more:
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.CirculationItemWebListInfo'
        type: array
    type: object
  define.GetCirculationItemWebOverviewResp:
    properties:
      hot:
        allOf:
        - $ref: '#/definitions/define.HotWebOverviewInfo'
        description: 热榜概览
      market_amount:
        allOf:
        - $ref: '#/definitions/define.MarketAmountWebOverviewInfo'
        description: 市值概览
      transaction_amount:
        allOf:
        - $ref: '#/definitions/define.TransactionAmountWebOverviewInfo'
        description: 成交额概览
    type: object
  define.GetCirculationItemWebTopListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.CirculationItemWebTopListInfo'
        type: array
    type: object
  define.GetConversationAdminDetailResp:
    properties:
      client_user_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 客户用户信息
      created_at:
        description: 创建时间
        type: string
      id:
        description: 会话记录ID
        type: string
      merchant_user_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 商家用户信息
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetConversationAdminListData:
    properties:
      client_user_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 客户用户信息
      created_at:
        description: 创建时间
        type: string
      id:
        description: 会话记录ID
        type: string
      last_message_time:
        description: 最后消息时间
        type: string
      merchant_user_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 商家用户信息
      message_count:
        description: 消息数量
        type: integer
    type: object
  define.GetConversationAdminListResp:
    properties:
      list:
        description: 会话列表
        items:
          $ref: '#/definitions/define.GetConversationAdminListData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.GetConversationDetailResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 会话记录ID
        type: string
      other_participant:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 对方信息
    type: object
  define.GetConversationListData:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 会话记录ID
        type: string
      last_message_content:
        description: 最后消息内容
        type: string
      last_message_time:
        description: 最后消息时间
        type: string
      last_message_type:
        allOf:
        - $ref: '#/definitions/enums.MessageType'
        description: 最后消息类型：1=文本 2=图片 3=帖子快照
      other_participant:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 对方信息
      unread_count:
        description: 未读消息数量
        type: integer
    type: object
  define.GetConversationListResp:
    properties:
      has_more:
        description: 是否有更多
        type: boolean
      list:
        description: 会话列表
        items:
          $ref: '#/definitions/define.GetConversationListData'
        type: array
      total_unread_count:
        description: 未读消息数量
        type: integer
    type: object
  define.GetExchangeLogListAdminReq:
    properties:
      end_time:
        type: string
      id:
        description: 积分商品 id/活动 id
        example: "0"
        type: string
      item_keyword:
        description: 商品搜索关键词
        type: string
      mobile_phone:
        description: 用户手机号
        type: string
      page:
        description: 页码
        type: integer
      page_size:
        description: 每页显示的条目数量
        type: integer
      sku_no:
        description: 商品 sku
        type: string
      start_time:
        type: string
      user_id:
        description: 用户 id
        type: string
    required:
    - page
    - page_size
    type: object
  define.GetExchangeLogListAdminResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.ExchangeLogAdminInfo'
        type: array
      total:
        type: integer
    type: object
  define.GetExchangeLogWebTopListItem:
    properties:
      avatar:
        description: 用户头像
        type: string
      item_name:
        description: 商品名称
        type: string
      nick_name:
        description: 用户昵称
        type: string
    type: object
  define.GetExchangeLogWebTopListReq:
    type: object
  define.GetExchangeLogWebTopListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetExchangeLogWebTopListItem'
        type: array
    type: object
  define.GetInviteAdminListResp:
    properties:
      avatar:
        type: string
      mobile_phone:
        type: string
      nickname:
        type: string
      reward_amount:
        type: integer
      rick_reason:
        description: 风控原因
        type: string
      rick_time:
        description: 分控时间
        type: string
      status:
        description: 1-正常 99-异常 100-可疑
        type: integer
      target_avatar:
        type: string
      target_mobile_phone:
        type: string
      target_nickname:
        type: string
      target_purchase_amount:
        type: integer
      target_real_name:
        type: string
      target_reg_time:
        type: string
      target_total_purchase_amount:
        type: integer
      target_user_id:
        type: string
      target_user_login_info:
        $ref: '#/definitions/model.UserLoginLog'
      total_reward:
        type: integer
      user_login_info:
        $ref: '#/definitions/model.UserLoginLog'
      userId:
        type: string
    type: object
  define.GetInviteSummaryResp:
    properties:
      current_bonus:
        description: 当前奖励余额
        type: integer
      invite_code:
        description: 用户邀请码
        type: string
      invite_config:
        $ref: '#/definitions/define.InviteRewardConfig'
      total_bonus:
        description: 累计奖励
        type: integer
      total_invite_count:
        description: 累计邀请数量
        type: integer
      userId:
        type: string
      wait_receive_bonus:
        description: 待领取奖励余额
        type: integer
    type: object
  define.GetIssueItemWebTopListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.IssueItemWebTopListInfo'
        type: array
    type: object
  define.GetItemIdInfoAdminList:
    properties:
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: ID       string `json:"id"`        // ID
        type: string
      item_name:
        description: 商品名称
        type: string
    type: object
  define.GetItemIdInfoWebList:
    properties:
      circulation_status:
        allOf:
        - $ref: '#/definitions/mongdb.CirculationStatusEnum'
        description: 流通状态
      item_id:
        description: ID           string  `json:"id"`            // ID
        type: string
      item_name:
        description: 商品名称
        type: string
      price_changes:
        description: 价格变动
        type: number
      status:
        allOf:
        - $ref: '#/definitions/mongdb.IssueItemStatusEnum'
        description: 状态
    type: object
  define.GetItemIsUpResp:
    properties:
      has_up:
        description: 0:没有上架 1:有上架
        type: integer
    type: object
  define.GetItemStoryIsUpResp:
    properties:
      has_up:
        description: 0:没有上架 1:有上架
        type: integer
    type: object
  define.GetItemStoryListData:
    properties:
      item_id:
        description: 物品ID
        type: string
      item_name:
        description: 物品名称
        type: string
      item_url:
        description: 物品图片
        type: string
      price:
        description: 发行价格
        type: integer
      stock:
        description: 总库存
        type: integer
      used_stock:
        description: 已用库存
        type: integer
    type: object
  define.GetItemStoryListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetItemStoryListData'
        type: array
      total:
        type: integer
    type: object
  define.GetItemSynthesisListData:
    properties:
      creator:
        description: 创建人
        type: string
      item_id:
        description: 物品ID
        type: string
      item_name:
        description: 物品名称
        type: string
      item_url:
        description: 物品图片
        type: string
      price:
        description: 发行价格
        type: integer
      usable_stock:
        description: 物品可用库存
        type: integer
    type: object
  define.GetItemSynthesisListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetItemSynthesisListData'
        type: array
      total:
        type: integer
    type: object
  define.GetJsSDKReq:
    properties:
      app_id:
        type: string
      url:
        type: string
    type: object
  define.GetJsSDKResp:
    properties:
      appId:
        type: string
      nonceStr:
        type: string
      signature:
        type: string
      timestamp:
        type: integer
    type: object
  define.GetMarketChangesAdminDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/app_service_apps_business_market_changes_define.GetCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 行情异动内容
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 行情异动ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/define.GetItemIdInfoAdminList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesMessagePushType'
        description: 消息推送
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesStatus'
        description: 状态
      title:
        description: 行情异动标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetMarketChangesAdminListData:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/app_service_apps_business_market_changes_define.GetCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 行情异动内容
        type: string
      created_at:
        description: AdIds          []string                       `json:"ad_ids"`            //
          关联广告ID列表
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 行情异动ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/define.GetItemIdInfoAdminList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.MarketChangesStatus'
        description: Priority       int32                                        `json:"priority"`           //
          优先级
      title:
        description: 行情异动标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetMarketChangesAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetMarketChangesAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetMarketChangesWebDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetCategoryWebLessDetailResp'
        description: 分类信息
      content:
        description: 行情异动内容
        type: string
      id:
        description: 行情异动ID
        example: "0"
        type: string
      is_can_jump:
        description: 是否可跳转
        type: boolean
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 行情异动标题
        type: string
    type: object
  define.GetMarketChangesWebListData:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      current_time:
        description: 当前时间
        type: string
      id:
        description: 行情异动ID
        example: "0"
        type: string
      is_can_jump:
        description: 是否可跳转
        type: boolean
      item_id_info_list:
        description: CategoryInfo   *GetCategoryWebLessDetailResp `json:"category_info"`      //
          分类信息
        items:
          $ref: '#/definitions/define.GetItemIdInfoWebList'
        type: array
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 行情异动标题
        type: string
    type: object
  define.GetMarketChangesWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetMarketChangesWebListData'
        type: array
    type: object
  define.GetMarketWebOverviewResp:
    properties:
      down_count:
        description: 跌
        type: integer
      flat_count:
        description: 平
        type: integer
      transaction_amount:
        description: 成交额（单位：分）
        type: integer
      up_count:
        description: 涨
        type: integer
      update_time:
        description: 更新时间
        type: string
    type: object
  define.GetMerchantApplicationAdminListData:
    properties:
      applied_at:
        description: 申请时间
        type: string
      id:
        description: 申请ID
        type: string
      reviewed_at:
        description: 审核时间
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.ApplicationStatus'
        description: 申请状态：-1=审核不通过 1=待审核 2=审核通过
      user_id:
        description: 用户ID
        type: string
      user_name:
        description: 用户名称
        type: string
    type: object
  define.GetMerchantApplicationAdminListResp:
    properties:
      list:
        description: 商家申请列表
        items:
          $ref: '#/definitions/define.GetMerchantApplicationAdminListData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.GetMerchantApplicationStatusResp:
    properties:
      id:
        description: 申请ID
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.ApplicationStatus'
        description: 申请状态：-1=审核不通过 1=待审核 2=审核通过
      user_id:
        description: 用户ID
        type: string
    type: object
  define.GetMessageAdminListData:
    properties:
      big_user_id:
        description: 较大的用户ID
        type: string
      client_msg_id:
        description: 客户端生成的消息ID，用于幂等性控制
        type: string
      content:
        description: 消息内容
        type: string
      created_at:
        description: 创建时间
        type: string
      direction:
        description: 消息方向：-1=较大uid向较小uid发送消息，1=较小uid向较大uid发送消息
        type: integer
      id:
        description: 消息ID
        type: string
      is_smart_reply:
        description: 是否为智能回复
        type: boolean
      media:
        allOf:
        - $ref: '#/definitions/define.MediaFile'
        description: 媒体文件，可能为空
      message_type:
        allOf:
        - $ref: '#/definitions/enums.MessageType'
        description: 消息类型：1=文本 2=图片 3=帖子快照
      post_id:
        description: 帖子ID（帖子消息类型时有值）
        type: string
      post_snapshot:
        allOf:
        - $ref: '#/definitions/define.PostSnapshot'
        description: 帖子快照信息（帖子消息类型时有值）
      small_user_id:
        description: 较小的用户ID
        type: string
    type: object
  define.GetMessageAdminListResp:
    properties:
      has_more:
        description: 是否有更多数据
        type: boolean
      list:
        description: 消息列表
        items:
          $ref: '#/definitions/define.GetMessageAdminListData'
        type: array
    type: object
  define.GetMessageListData:
    properties:
      client_msg_id:
        description: 客户端生成的消息ID，用于幂等性控制
        type: string
      content:
        description: 消息内容
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 消息ID
        type: string
      is_sent:
        description: 是否为当前用户发送的消息
        type: boolean
      is_smart_reply:
        description: 是否为智能回复
        type: boolean
      media:
        allOf:
        - $ref: '#/definitions/define.MediaFile'
        description: 媒体文件，可能为空
      message_type:
        allOf:
        - $ref: '#/definitions/enums.MessageType'
        description: 消息类型：1=文本 2=图片 3=帖子快照
      post_id:
        description: 帖子ID（帖子消息类型时有值）
        type: string
      post_snapshot:
        allOf:
        - $ref: '#/definitions/define.PostSnapshot'
        description: 帖子快照信息（帖子消息类型时有值）
    type: object
  define.GetMessageListResp:
    properties:
      has_more:
        description: 是否有更多
        type: boolean
      list:
        description: 消息列表
        items:
          $ref: '#/definitions/define.GetMessageListData'
        type: array
    type: object
  define.GetMyPostListData:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 帖子描述
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      price:
        description: 收购价格
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
    type: object
  define.GetMyPostListResp:
    properties:
      has_more:
        description: 是否有更多
        type: boolean
      list:
        description: 帖子列表
        items:
          $ref: '#/definitions/define.GetMyPostListData'
        type: array
    type: object
  define.GetOperationAnnCategoryAdminDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnCategoryStatus'
        description: 状态
      text_color:
        description: 文字颜色
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetOperationAnnCategoryAdminLessDetailResp:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  define.GetOperationAnnCategoryAdminListData:
    properties:
      ann_num:
        description: 运营公告数量
        type: integer
      created_at:
        description: 创建时间
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      priority:
        description: 优先级
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnCategoryStatus'
        description: 状态
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetOperationAnnCategoryAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetOperationAnnCategoryAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetOperationAnnCategoryWebLessDetailResp:
    properties:
      background_color:
        description: 背景颜色
        type: string
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
      text_color:
        description: 文字颜色
        type: string
    type: object
  define.GetOperationAnnCategoryWebListData:
    properties:
      id:
        description: 分类ID
        example: "0"
        type: string
      name:
        description: 分类名称
        type: string
    type: object
  define.GetOperationAnnCategoryWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetOperationAnnCategoryWebListData'
        type: array
    type: object
  define.GetOperationAnnouncementAdminDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetOperationAnnCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 运营公告内容
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 运营公告ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/app_service_apps_business_operation_announcement_define.GetItemIdInfoList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      message_push:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementMessagePushEnum'
        description: 消息推送【1:推送, 2:不推送】
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementStatus'
        description: 状态
      title:
        description: 运营公告标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetOperationAnnouncementAdminListData:
    properties:
      ad_ids:
        description: CreatorInfo   *GetUserInfoAdminResp             `json:"creator_info"`       //
          创建人信息
        items:
          type: string
        type: array
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetOperationAnnCategoryAdminLessDetailResp'
        description: 分类信息
      channel_ids:
        description: 关联渠道ID列表
        items:
          type: string
        type: array
      content:
        description: 运营公告内容
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      id:
        description: 运营公告ID
        example: "0"
        type: string
      item_id_info_list:
        description: 关联商品列表
        items:
          $ref: '#/definitions/app_service_apps_business_operation_announcement_define.GetItemIdInfoList'
        type: array
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      priority:
        description: 优先级
        type: integer
      publish_time:
        description: 发布时间
        type: string
      publish_type:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementPublishType'
        description: 发布类型
      status:
        allOf:
        - $ref: '#/definitions/enums.OperationAnnouncementStatus'
        description: 状态
      title:
        description: 运营公告标题
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetOperationAnnouncementAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetOperationAnnouncementAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetOperationAnnouncementWebDetailResp:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetOperationAnnCategoryWebLessDetailResp'
        description: 分类信息
      content:
        description: 运营公告内容
        type: string
      id:
        description: 运营公告ID
        example: "0"
        type: string
      item_ids:
        description: 关联商品ID列表
        items:
          type: string
        type: array
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 运营公告标题
        type: string
    type: object
  define.GetOperationAnnouncementWebListData:
    properties:
      category_id:
        description: 分类ID
        example: "0"
        type: string
      category_info:
        allOf:
        - $ref: '#/definitions/define.GetOperationAnnCategoryWebLessDetailResp'
        description: 分类信息
      current_time:
        description: 当前时间
        type: string
      id:
        description: 运营公告ID
        example: "0"
        type: string
      publish_time:
        description: 发布时间
        type: string
      title:
        description: 运营公告标题
        type: string
    type: object
  define.GetOperationAnnouncementWebListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetOperationAnnouncementWebListData'
        type: array
    type: object
  define.GetOperationLogListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.OperationLogItem'
        type: array
    type: object
  define.GetPostAdminDetailResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 帖子描述
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      merchant_id:
        description: 商家ID
        type: string
      merchant_name:
        description: 商家名称
        type: string
      price:
        description: 收购价格
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetPostAdminListData:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 帖子描述
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      merchant_id:
        description: 商家ID
        type: string
      merchant_name:
        description: 商家名称
        type: string
      price:
        description: 收购价格
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
    type: object
  define.GetPostAdminListResp:
    properties:
      list:
        description: 帖子列表
        items:
          $ref: '#/definitions/define.GetPostAdminListData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.GetPostDetailResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 帖子描述
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      merchant_id:
        description: 商家ID
        type: string
      merchant_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 商家信息
      price:
        description: 收购价格
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetPostListData:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 帖子描述
        type: string
      id:
        description: 帖子ID
        type: string
      media_files:
        description: 媒体文件
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      merchant_id:
        description: 商家ID
        type: string
      merchant_info:
        allOf:
        - $ref: '#/definitions/define.UserInfo'
        description: 商家信息
      price:
        description: 收购价格
        type: integer
    type: object
  define.GetPostListResp:
    properties:
      has_more:
        description: 是否有更多
        type: boolean
      list:
        description: 帖子列表
        items:
          $ref: '#/definitions/define.GetPostListData'
        type: array
    type: object
  define.GetPriorityBuyIsUpResp:
    properties:
      has_up:
        description: 0:没有上架 1:有上架
        type: integer
    type: object
  define.GetPriorityBuyItem:
    properties:
      advance_minutes:
        description: 优先购开始时间
        type: integer
      status:
        description: 优先购状态
        type: integer
      stock:
        description: 优先购库存
        type: integer
      used_stock:
        description: 优先购已用库存
        type: integer
    type: object
  define.GetPriorityBuyListData:
    properties:
      creator:
        type: string
      id:
        description: 优先购ID
        type: string
      issue_item_id:
        description: 一手物品ID
        type: string
      item_id:
        description: 物品ID
        type: string
      item_name:
        description: 物品名称
        type: string
      item_url:
        description: 物品图片
        type: string
      name:
        description: 优先购名称
        type: string
      price:
        description: 发行价格
        type: integer
      priority_buy:
        allOf:
        - $ref: '#/definitions/define.GetPriorityBuyItem'
        description: 优先购配置
    type: object
  define.GetPriorityBuyListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetPriorityBuyListData'
        type: array
      total:
        type: integer
    type: object
  define.GetShipManageAdminDetailResp:
    properties:
      _id:
        description: 订单ID
        type: string
      admin_remark:
        description: 管理员备注
        type: string
      app_channel:
        description: Address    OrderAddress     `json:"address"`     // 地址信息
        type: string
      app_version:
        description: 应用版本
        type: string
      client_type:
        description: 客户端类型
        type: string
      created_at:
        description: 创建时间
        type: string
      expire:
        description: 过期时间(秒)
        type: integer
      extends:
        allOf:
        - $ref: '#/definitions/yc_open.OrderExtends'
        description: 订单扩展信息
      freights:
        description: 物流信息
        items:
          $ref: '#/definitions/define.Freight'
        type: array
      items:
        description: 商品列表
        items:
          $ref: '#/definitions/define.OrderItem'
        type: array
      open_user_id:
        description: |-
          User           OrderUser                      `json:"user"`             // 用户信息
          UserID     string           `json:"user_id"` // 用户ID
        type: string
      order_type:
        allOf:
        - $ref: '#/definitions/enums.OrderType'
        description: 订单来源：1：云仓提货:2：文潮提货。
      pay_amount:
        description: 总实付金额
        type: integer
      ship_manage_id:
        description: 发货管理ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/define.ItemWithdrawOrderStatus'
        description: 订单状态
      updated_at:
        description: 更新时间
        type: string
      user:
        allOf:
        - $ref: '#/definitions/define.OrderUser'
        description: 用户信息
      user_id:
        description: 用户ID
        type: string
      user_item_amount:
        description: 用户商品数量
        type: integer
      withdraw_type:
        allOf:
        - $ref: '#/definitions/enums.WithdrawType'
        description: '提货方式：1：手动提货: 2：强制发货。'
      yc_id:
        description: 云仓ID
        type: string
    type: object
  define.GetShipManageAdminListData:
    properties:
      _id:
        description: 订单ID
        type: string
      created_at:
        description: 创建时间
        type: string
      extends:
        allOf:
        - $ref: '#/definitions/yc_open.OrderExtends'
        description: 订单扩展信息
      items:
        description: 商品列表
        items:
          $ref: '#/definitions/define.OrderItem'
        type: array
      open_user_id:
        description: |-
          Items     []OrderItem                    `json:"items"`      // 商品信息列表：[非空]表示该订单真实商品
          User      OrderUser                      `json:"user"`       // 用户信息：[1]玩家, [2]主播
          UserID     string `json:"user_id"` // 云仓用户ID
        type: string
      order_type:
        allOf:
        - $ref: '#/definitions/enums.OrderType'
        description: 订单来源：1：云仓提货:2：文潮提货。
      pay_amount:
        description: 实付金额
        type: integer
      ship_manage_id:
        description: 发货管理ID
        example: "0"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/define.ItemWithdrawOrderStatus'
        description: 订单状态
      updated_at:
        description: 更新时间
        type: string
      user:
        allOf:
        - $ref: '#/definitions/define.OrderUser'
        description: 用户信息
      user_id:
        description: 用户ID
        type: string
      withdraw_type:
        allOf:
        - $ref: '#/definitions/enums.WithdrawType'
        description: '提货方式：1：手动提货: 2：强制发货。'
      yc_id:
        description: 云仓ID
        type: string
    type: object
  define.GetShipManageAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetShipManageAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetShipManageMobilePhoneResp:
    properties:
      mobile_phone:
        description: 手机号
        type: string
    type: object
  define.GetShipManageOrderLogsResp:
    properties:
      list:
        items:
          $ref: '#/definitions/yc_open.OrderLogsData'
        type: array
      total:
        type: integer
    type: object
  define.GetSmartReplyTemplateResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 模板ID
        type: string
      is_enabled:
        description: 是否启用
        type: boolean
      merchant_id:
        description: 商家ID
        type: string
      template_content:
        description: 模板内容
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.GetStoryAdminDetailResp:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      complete_user_num:
        description: 已合人数(按用户去重)
        type: integer
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      end_time:
        description: 结束时间
        type: string
      id:
        description: 故事玩法ID
        type: integer
      item_id:
        description: 故事玩法物品id(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      release_data:
        allOf:
        - $ref: '#/definitions/define.StoryReleaseTime'
        description: 故事玩法材料释放时间
      scene_id:
        description: 故事玩法场景id
        type: integer
      scene_name:
        description: 场景名称
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      story_item_data:
        allOf:
        - $ref: '#/definitions/define.StoryItemData'
        description: 故事玩法奖品
      story_materials:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsDetailData'
        type: array
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      updated_at:
        description: 更新时间
        type: string
      updated_by:
        description: 更新人
        type: string
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    type: object
  define.GetStoryAdminListData:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      complete_user_num:
        description: 已合人数(按用户去重)
        type: integer
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      end_time:
        description: 结束时间
        type: string
      id:
        description: 故事玩法ID
        type: integer
      item_id:
        description: 故事玩法物品id(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      scene_name:
        description: 场景名称
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      updated_at:
        description: 更新时间
        type: string
      updated_by:
        description: 更新人
        type: string
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    type: object
  define.GetStoryAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStoryAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetStoryAdminOrderDetailResp:
    properties:
      activity_id:
        description: 故事玩法ID
        type: integer
      activity_title:
        description: 活动名称
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      chain_hash:
        description: 上链哈希
        type: string
      complete_time:
        description: 探索结束时间
        type: string
      created_at:
        description: 创建时间
        type: string
      finish_time:
        description: 完成时间
        type: string
      id:
        description: 订单ID
        example: "0"
        type: string
      item_detail:
        allOf:
        - $ref: '#/definitions/define.GetStoryAdminOrderItemDetail'
        description: 故事玩法物品
      materials_detail:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.GetStoryAdminOrderMaterialsDetail'
        type: array
      order_id:
        description: 订单号
        type: string
      qty:
        description: 故事玩法数量
        type: integer
      status:
        description: 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
    type: object
  define.GetStoryAdminOrderItemDetail:
    properties:
      code:
        description: 编码
        type: string
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 故事玩法材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_title:
        description: 物品名称
        type: string
    type: object
  define.GetStoryAdminOrderListData:
    properties:
      activity_id:
        description: 故事玩法ID
        type: integer
      activity_title:
        description: 活动名称
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      chain_status:
        description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        type: integer
      created_at:
        description: 故事玩法时间
        type: string
      id:
        description: 订单ID
        example: "0"
        type: string
      item_cover_url:
        description: 故事玩法物品封面图
        type: string
      item_title:
        description: 故事玩法物品
        type: string
      order_id:
        description: 订单号
        type: string
      qty:
        description: 故事玩法数量
        type: integer
      scene_name:
        description: 故事玩法场景名称
        type: string
      status:
        description: 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        type: integer
      user_id:
        description: 用户ID
        type: string
    type: object
  define.GetStoryAdminOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStoryAdminOrderListData'
        type: array
      total:
        type: integer
    type: object
  define.GetStoryAdminOrderMaterialsDetail:
    properties:
      code:
        description: 编码
        type: string
      cost_price:
        description: 成本价
        type: integer
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 故事玩法材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_name:
        description: 物品名称
        type: string
      user_item_id:
        description: 用户物品ID
        type: string
    type: object
  define.GetStoryJoinWebData:
    properties:
      count:
        description: 探索中的次数
        type: integer
      image_urls:
        description: 商品图片列表
        items:
          type: string
        type: array
    type: object
  define.GetStoryLogListResp:
    properties:
      action:
        description: 操作类型【1-创建；2-修改；3-删除；4-上架；5-下架】
        example: 1
        type: integer
      content:
        description: 变动字段列表，修改才有，故事玩法条件有修改会返回materials，只需要展示文字"修改故事玩法条件"
        example:
        - activity_desc
        - title
        items:
          type: string
        type: array
      created_at:
        description: 创建时间
        example: "2025-04-01T11:44:22.384+08:00"
        type: string
      created_by:
        description: 创建人
        example: 张三
        type: string
    type: object
  define.GetStoryOrderDetailListData:
    properties:
      code:
        description: 编码
        type: string
      cost_price:
        description: 成本价
        type: integer
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 合成材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_name:
        description: 物品名称
        type: string
      user_item_id:
        description: 用户物品ID
        type: string
    type: object
  define.GetStoryOrderDetailListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStoryOrderDetailListData'
        type: array
      total:
        type: integer
    type: object
  define.GetStoryOrderDetailMaterials:
    properties:
      item_name:
        description: 故事玩法材料名称
        type: string
      item_url:
        description: 故事玩法材料图片
        type: string
      qty:
        description: 故事玩法材料数量
        type: integer
    type: object
  define.GetStoryOrderListData:
    properties:
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 派遣时间
        type: string
      end_time:
        description: 结束时间
        type: string
      item_cover_url:
        description: 故事玩法物品图片(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      order_id:
        description: 故事玩法订单编号
        type: string
      qty:
        description: 集齐数量
        type: integer
      scene_name:
        description: 场景名称
        type: string
      status:
        description: 订单状态【21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        type: integer
      title:
        description: 活动名称
        type: string
    type: object
  define.GetStoryRuleAdminResp:
    properties:
      content:
        type: string
    type: object
  define.GetStorySceneAdminDetailResp:
    properties:
      content:
        description: 场景介绍
        type: string
      cover_url:
        description: 封面图
        type: string
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
    type: object
  define.GetStorySceneAdminListData:
    properties:
      cover_url:
        description: 封面图
        type: string
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
      story_num:
        description: 活动数量
        type: integer
    type: object
  define.GetStorySceneAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStorySceneAdminListData'
        type: array
    type: object
  define.GetStorySceneWebData:
    properties:
      content:
        description: 介绍
        type: string
      cover_url:
        description: 封面图
        type: string
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
    type: object
  define.GetStorySceneWebHomeData:
    properties:
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
      status:
        description: 故事玩法状态 20:即将开始;21:进行中
        type: integer
    type: object
  define.GetStorySceneWebHomeResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStorySceneWebHomeData'
        type: array
    type: object
  define.GetStorySceneWebListData:
    properties:
      cover_url:
        description: 封面图
        type: string
      id:
        description: 场景ID
        type: integer
      name:
        description: 场景名称
        type: string
      story_num:
        description: 活动数量
        type: integer
      user_name_list:
        description: 用户昵称列表
        items:
          type: string
        type: array
    type: object
  define.GetStorySceneWebListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStorySceneWebListData'
        type: array
    type: object
  define.GetStoryUserMaterialsListData:
    properties:
      cost_price:
        description: 持仓成本
        type: integer
      fusion_tags:
        description: 融合标签【0:否;1:是】
        type: integer
      item_cover_url:
        description: 故事玩法物品图片
        type: string
      item_id:
        description: 故事玩法物品id
        type: string
      item_title:
        description: 故事玩法物品名称
        type: string
      story_tags:
        description: 故事玩法标签【0:否;1:是】
        type: integer
      user_item_id:
        description: 用户物品id
        type: string
    type: object
  define.GetStoryUserMaterialsListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStoryUserMaterialsListData'
        type: array
      total:
        type: integer
    type: object
  define.GetStoryWebDetailResp:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      current_time:
        description: 服务器当前时间
        type: string
      end_time:
        description: 结束时间
        type: string
      item_id:
        description: 故事玩法物品id(商品/组合商品)
        type: string
      item_image_url:
        description: 故事玩法物品图片
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      max_limit:
        description: 一次限合最大数量
        type: integer
      start_time:
        description: 开始时间
        type: string
      status:
        description: 故事玩法状态 20:即将开始;21:进行中;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
      user_story_materials:
        description: 用户故事玩法材料
        items:
          $ref: '#/definitions/define.UserStoryMaterials'
        type: array
    type: object
  define.GetStoryWebListData:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      end_time:
        description: 结束时间
        type: string
      issuer_name:
        description: 发行名称
        type: string
      issuer_short_name:
        description: 发行方简称
        type: string
      seller_name:
        description: 销售方全称
        type: string
      seller_short_name:
        description: 销售方简称
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 故事玩法状态 20:即将开始;21:进行中;4:已结束
        type: integer
      story_join_data:
        allOf:
        - $ref: '#/definitions/define.GetStoryJoinWebData'
        description: 用户参与数据
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
    type: object
  define.GetStoryWebListResp:
    properties:
      current_time:
        description: 服务器当前时间
        type: string
      list:
        items:
          $ref: '#/definitions/define.GetStoryWebListData'
        type: array
      story_scene_data:
        allOf:
        - $ref: '#/definitions/define.GetStorySceneWebData'
        description: 故事玩法场景数据
      total:
        type: integer
    type: object
  define.GetStoryWebOrderDetailResp:
    properties:
      activity_type:
        description: 故事玩法类型【1-组合商品；2-商品】
        type: integer
      chain_hash:
        description: 故事玩法链上hash
        type: string
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 派遣时间
        type: string
      end_time:
        description: 结束时间
        type: string
      item_cover_url:
        description: 故事玩法物品图片(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      materials:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.GetStoryOrderDetailMaterials'
        type: array
      order_id:
        description: 故事玩法订单编号
        type: string
      qty:
        description: 集齐数量
        type: integer
      status:
        description: 订单状态【21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        type: integer
    type: object
  define.GetStoryWebOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetStoryOrderListData'
        type: array
      total:
        type: integer
    type: object
  define.GetStoryWebOrderStatusResp:
    properties:
      item_cover_url:
        description: 故事玩法物品图片
        type: string
      item_id:
        description: 故事玩法物品id
        type: string
      item_title:
        description: 故事玩法物品名称
        type: string
      qty:
        description: 故事玩法数量
        type: integer
      status:
        description: 状态【-1:失败;21:成功】
        type: integer
    type: object
  define.GetSynthesisAdminDetailResp:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      complete_user_num:
        description: 已合人数(按用户去重)
        type: integer
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      end_time:
        description: 结束时间
        type: string
      id:
        description: 合成活动ID
        type: integer
      item_data:
        allOf:
        - $ref: '#/definitions/define.SynthesisPrizeData'
        description: 融合商品信息
      item_id:
        description: 合成物品id(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      priority_buy_data:
        allOf:
        - $ref: '#/definitions/define.SynthesisPrizeData'
        description: 优先购信息
      release_data:
        allOf:
        - $ref: '#/definitions/define.SynthesisReleaseTime'
        description: 融合材料释放时间
      start_time:
        description: 开始时间
        type: string
      status:
        description: 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      stock_display:
        description: 剩余库存是否显示【1:显示;2:不显示】
        type: integer
      synthesis_materials:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsDetailData'
        type: array
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      updated_at:
        description: 更新时间
        type: string
      updated_by:
        description: 更新人
        type: string
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    type: object
  define.GetSynthesisAdminListData:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      complete_user_num:
        description: 已合人数(按用户去重)
        type: integer
      cover_url:
        description: 封面图
        type: string
      created_at:
        description: 创建时间
        type: string
      created_by:
        description: 创建人
        type: string
      end_time:
        description: 结束时间
        type: string
      id:
        description: 合成活动ID
        type: integer
      item_id:
        description: 合成物品id(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      updated_at:
        description: 更新时间
        type: string
      updated_by:
        description: 更新人
        type: string
      user_limit:
        description: 每人限合(0不限制)
        type: integer
    type: object
  define.GetSynthesisAdminListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetSynthesisAdminListData'
        type: array
      total:
        type: integer
    type: object
  define.GetSynthesisAdminOrderDetailResp:
    properties:
      activity_id:
        description: 合成活动ID
        type: integer
      activity_title:
        description: 活动名称
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      chain_hash:
        description: 上链哈希
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 订单ID
        example: "0"
        type: string
      item_detail:
        allOf:
        - $ref: '#/definitions/define.GetSynthesisAdminOrderItemDetail'
        description: 融合物品
      materials_detail:
        description: 融合材料
        items:
          $ref: '#/definitions/define.GetSynthesisAdminOrderMaterialsDetail'
        type: array
      order_id:
        description: 订单号
        type: string
      qty:
        description: 合成数量
        type: integer
      status:
        description: 订单状态
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
    type: object
  define.GetSynthesisAdminOrderItemDetail:
    properties:
      code:
        description: 编码
        type: string
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 合成材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_title:
        description: 物品名称
        type: string
    type: object
  define.GetSynthesisAdminOrderListData:
    properties:
      activity_id:
        description: 合成活动ID
        type: integer
      activity_title:
        description: 活动名称
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      chain_status:
        description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        type: integer
      created_at:
        description: 融合时间
        type: string
      id:
        description: 订单ID
        example: "0"
        type: string
      item_cover_url:
        description: 融合物品封面图
        type: string
      item_title:
        description: 融合物品
        type: string
      order_id:
        description: 订单号
        type: string
      qty:
        description: 合成数量
        type: integer
      status:
        description: 订单状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
        type: integer
      user_id:
        description: 用户ID
        type: string
    type: object
  define.GetSynthesisAdminOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetSynthesisAdminOrderListData'
        type: array
      total:
        type: integer
    type: object
  define.GetSynthesisAdminOrderMaterialsDetail:
    properties:
      code:
        description: 编码
        type: string
      cost_price:
        description: 成本价
        type: integer
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 合成材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_name:
        description: 物品名称
        type: string
      user_item_id:
        description: 用户物品ID
        type: string
    type: object
  define.GetSynthesisLogListResp:
    properties:
      action:
        description: 操作类型【1-创建；2-修改；3-删除；4-上架；5-下架】
        example: 1
        type: integer
      content:
        description: 变动字段列表，修改才有，融合条件有修改会返回materials，只需要展示文字"修改融合条件"
        example:
        - activity_desc
        - title
        items:
          type: string
        type: array
      created_at:
        description: 创建时间
        example: "2025-04-01T11:44:22.384+08:00"
        type: string
      created_by:
        description: 创建人
        example: 张三
        type: string
    type: object
  define.GetSynthesisOrderDetailListData:
    properties:
      code:
        description: 编码
        type: string
      cost_price:
        description: 成本价
        type: integer
      cover_url:
        description: 物品封面图
        type: string
      id:
        description: 合成材料物品ID
        type: string
      ip:
        description: IP
        type: string
      issue_price:
        description: 发行价格
        type: integer
      issuer:
        description: 发行方
        type: string
      item_name:
        description: 物品名称
        type: string
      user_item_id:
        description: 用户物品ID
        type: string
    type: object
  define.GetSynthesisOrderDetailListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetSynthesisOrderDetailListData'
        type: array
      total:
        type: integer
    type: object
  define.GetSynthesisOrderDetailMaterials:
    properties:
      item_name:
        description: 合成材料名称
        type: string
      item_url:
        description: 合成材料图片
        type: string
      qty:
        description: 合成材料数量
        type: integer
    type: object
  define.GetSynthesisOrderListData:
    properties:
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      created_at:
        description: 创建时间
        type: string
      item_cover_url:
        description: 合成物品图片(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      order_id:
        description: 合成订单编号
        type: string
      qty:
        description: 集齐数量
        type: integer
    type: object
  define.GetSynthesisRuleAdminResp:
    properties:
      content:
        type: string
    type: object
  define.GetSynthesisWebDetailResp:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_desc:
        description: 说明
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      current_time:
        description: 服务器当前时间
        type: string
      end_time:
        description: 结束时间
        type: string
      item_id:
        description: 合成物品id(商品/优先购)
        type: string
      item_image_url:
        description: 合成物品图片
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      max_limit:
        description: 一次限合最大数量
        type: integer
      start_time:
        description: 开始时间
        type: string
      status:
        description: 合成活动状态2:已上架;4:已结束
        type: integer
      stock:
        description: 剩余库存
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
      user_limit:
        description: 每人限合(0不限制)
        type: integer
      user_synthesis_materials:
        description: 用户融合材料
        items:
          $ref: '#/definitions/define.UserSynthesisMaterials'
        type: array
    type: object
  define.GetSynthesisWebListData:
    properties:
      activity_code:
        description: 活动编码
        type: string
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      cover_url:
        description: 封面图
        type: string
      end_time:
        description: 结束时间
        type: string
      issuer_name:
        description: 发行名称
        type: string
      issuer_short_name:
        description: 发行方简称
        type: string
      seller_name:
        description: 销售方全称
        type: string
      seller_short_name:
        description: 销售方简称
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 合成活动状态2:已上架;4:已结束
        type: integer
      title:
        description: 活动名称
        type: string
      total_stock:
        description: 总库存
        type: integer
    type: object
  define.GetSynthesisWebListResp:
    properties:
      current_time:
        description: 服务器当前时间
        type: string
      list:
        items:
          $ref: '#/definitions/define.GetSynthesisWebListData'
        type: array
      total:
        type: integer
    type: object
  define.GetSynthesisWebOrderDetailResp:
    properties:
      activity_type:
        description: 合成类型【1-优先购权益；2-商品】
        type: integer
      chain_hash:
        description: 合成链上hash
        type: string
      created_at:
        description: 创建时间
        type: string
      item_cover_url:
        description: 合成物品图片(商品/优先购)
        type: string
      item_title:
        description: 合成物品名称(商品/优先购)
        type: string
      materials:
        description: 合成材料
        items:
          $ref: '#/definitions/define.GetSynthesisOrderDetailMaterials'
        type: array
      order_id:
        description: 合成订单编号
        type: string
      qty:
        description: 集齐数量
        type: integer
    type: object
  define.GetSynthesisWebOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetSynthesisOrderListData'
        type: array
      total:
        type: integer
    type: object
  define.GetSynthesisWebOrderStatusResp:
    properties:
      item_cover_url:
        description: 合成物品图片
        type: string
      item_id:
        description: 合成物品id
        type: string
      item_title:
        description: 合成物品名称
        type: string
      qty:
        description: 合成数量
        type: integer
      status:
        description: 状态【-1:失败;91:成功】
        type: integer
    type: object
  define.GetUserBonusLogResp:
    properties:
      amount:
        description: 数额
        type: integer
      created_at:
        type: string
      is_expiring_soon:
        description: 1-Yes 2-No
        type: integer
      main_img:
        description: 图片
        type: string
      name:
        description: 名字
        type: string
      receive_status:
        description: 1-待领取 10-未解锁 20-已领取 30-已使用 99-过期未领取
        type: integer
      source:
        description: 来源
        type: string
      user_bonus_log_id:
        type: integer
      userId:
        type: string
    type: object
  define.GetUserExchangeLogWebListInfo:
    properties:
      bonus_total:
        description: 消耗积分
        type: integer
      exchange_qty:
        description: 兑换数量
        type: integer
      exchange_time:
        description: 兑换时间
        type: string
      failed_reason:
        description: 兑换失败原因
        type: string
      icon_url:
        description: 商品主图
        type: string
      id:
        example: "0"
        type: string
      item_name:
        description: 商品名称
        type: string
      status:
        description: 状态，0：处理中，1：成功，2：失败
        type: integer
    type: object
  define.GetUserExchangeLogWebListReq:
    properties:
      page:
        description: 页码
        type: integer
      page_size:
        description: 每页显示的条目数量
        type: integer
    required:
    - page
    - page_size
    type: object
  define.GetUserExchangeLogWebListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetUserExchangeLogWebListInfo'
        type: array
      total:
        type: integer
    type: object
  define.GetUserInfoReq:
    type: object
  define.GetUserInfoResp:
    properties:
      invite_code:
        type: string
      inviteCount:
        type: integer
      is_merchant:
        description: 是否为商家
        type: boolean
      userId:
        type: string
    type: object
  define.GetUserInviteListResp:
    properties:
      amount:
        type: integer
      avatar:
        type: string
      nickname:
        type: string
      reg_time:
        type: string
      status:
        description: 1-已邀请 10-已实名
        type: integer
      userId:
        type: string
    type: object
  define.GetWebActivitiesResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.ActivityWebInfo'
        type: array
    type: object
  define.GetWebAllItemsResp:
    properties:
      items:
        items:
          $ref: '#/definitions/define.Item'
        type: array
    type: object
  define.GetWebStatisticOverviewResp:
    properties:
      hold_quantity:
        description: 持仓数量
        type: integer
      today_profit_loss_percentage:
        description: 今日盈亏百分比
        type: number
      today_profit_loss_value:
        description: 今日盈亏
        type: integer
      total_cost:
        description: 持仓成本
        type: integer
      total_market_value:
        description: 持仓市值
        type: integer
      total_profit_loss_value:
        description: 持仓盈亏
        type: integer
    type: object
  define.GetWebUserItemListResp:
    properties:
      tip:
        type: integer
      user_item_list:
        items:
          $ref: '#/definitions/define.UserItem'
        type: array
    type: object
  define.HotWebOverviewInfo:
    properties:
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      rate:
        description: 涨跌幅
        type: number
    type: object
  define.InviteRewardConfig:
    properties:
      banner_list:
        description: 广告位配置
        items:
          $ref: '#/definitions/define.Banner'
        type: array
      content_text_list:
        description: 分享文案列表
        items:
          type: string
        type: array
      expire_time:
        description: 过期时间，单位：天
        type: integer
      invite_reward_amount:
        description: 邀友返利金额
        type: integer
      invite_reward_enable:
        description: 邀友返利开关 1-Yes 2-No
        type: integer
      new_user_reward_amount:
        description: 新客奖励金额
        type: integer
      new_user_reward_enable:
        description: 新客奖励开关 1-Yes 2-No
        type: integer
      purchase_reward_amount:
        description: 消费奖励比例 0～100
        type: integer
      purchase_reward_enable:
        description: 消费奖励开关 1-Yes 2-No
        type: integer
      rule_desc:
        description: 规则说明
        type: string
    type: object
  define.IssueItemWebTopListInfo:
    properties:
      id:
        type: string
      image_url:
        type: string
      item_id:
        type: string
      item_name:
        type: string
      quantity:
        type: integer
      top_tag:
        description: '标签，new: 新，rush: 抢'
        type: string
    type: object
  define.Item:
    properties:
      delivery_time:
        type: string
      hold_quantity:
        type: integer
      icon_url:
        type: string
      item_id:
        type: string
      item_name:
        type: string
      last_market_price:
        type: integer
      market_value:
        type: integer
      profit_loss_value:
        type: integer
      profit_lost_percentage:
        type: number
      story_status:
        $ref: '#/definitions/mongdb.IssueItemStoryStatusEnum'
      synthesis_status:
        $ref: '#/definitions/mongdb.IssueItemSynthesisStatusEnum'
      total_cost:
        type: integer
    type: object
  define.ItemWithdrawOrderStatus:
    enum:
    - -2
    - -1
    - 0
    - 1
    - 2
    - 3
    - 4
    - 10
    - 20
    - 30
    - 35
    - 36
    - 39
    - 40
    - 50
    - 60
    - 61
    format: int32
    type: integer
    x-enum-varnames:
    - WithdrawOrderStatusAbandoned
    - WithdrawOrderStatusDeleted
    - WithdrawOrderStatusExpired
    - WithdrawOrderStatusDone
    - WithdrawOrderStatusAftersaleing
    - WithdrawOrderStatusAfterSaleRefunding
    - WithdrawOrderStatusAfterSaleDone
    - WithdrawOrderStatusCreated
    - WithdrawOrderStatusLocked
    - WithdrawOrderStatusChecked
    - WithdrawOrderStatusWaitForPay
    - WithdrawOrderStatusWaitForShip
    - WithdrawOrderStatusPartShiped
    - WithdrawOrderStatusShiped
    - WithdrawOrderStatusReceived
    - WithdrawOrderStatusWaitForMake
    - WithdrawOrderStatusWaitForDue
  define.LaunchSynthesisMaterials:
    properties:
      id:
        description: 合成材料id
        type: integer
      itemIds:
        description: 融合材料物品id集合
        items:
          type: string
        type: array
    type: object
  define.LaunchSynthesisReq:
    properties:
      activity_code:
        description: 活动编码
        type: string
      qty:
        description: 合成数量
        minimum: 1
        type: integer
      synthesis_materials:
        description: 选中合成材料
        items:
          $ref: '#/definitions/define.LaunchSynthesisMaterials'
        type: array
    required:
    - activity_code
    - qty
    - synthesis_materials
    type: object
  define.LaunchSynthesisResp:
    properties:
      item_cover_url:
        description: 合成物品图片
        type: string
      item_id:
        description: 合成物品id
        type: string
      item_title:
        description: 合成物品名称
        type: string
      order_id:
        description: 订单号
        type: string
      qty:
        description: 合成数量
        type: integer
    type: object
  define.MarketAmountWebOverviewInfo:
    properties:
      amount:
        description: 历史累计市值（单位：分）
        type: integer
      rate:
        description: 市值百分比
        type: number
    type: object
  define.MediaFile:
    properties:
      duration:
        description: 视频时长(秒)，图片为0
        type: integer
      height:
        description: 高度(像素)
        type: integer
      size:
        description: 文件大小(字节)
        type: integer
      thumbnail_url:
        description: 缩略图URL
        type: string
      type:
        allOf:
        - $ref: '#/definitions/enums.MediaType'
        description: 媒体类型：1=图片 2=视频
      url:
        description: 文件URL
        type: string
      width:
        description: 宽度(像素)
        type: integer
    required:
    - height
    - size
    - type
    - url
    - width
    type: object
  define.OperationLogItem:
    properties:
      action:
        $ref: '#/definitions/enum.OperationLogActionEnum'
      after_content:
        type: string
      before_content:
        type: string
      operated_at:
        type: string
      operated_by:
        type: string
      operation_log_id:
        type: integer
    type: object
  define.OrderItem:
    properties:
      icon_url:
        description: 商品主图
        type: string
      id:
        description: 商品ID
        type: string
      ip_classify_names:
        description: 商品IP
        items:
          type: string
        type: array
      issuer_name:
        description: 发行方全称
        type: string
      issuer_short_name:
        description: 发行方简称
        type: string
      item_id:
        description: 挂牌编码
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      price:
        description: 发行价格
        type: integer
      sku_no:
        description: sku_no
        type: string
    type: object
  define.OrderPatbgDetail:
    properties:
      avatar:
        description: 头像URL
        type: string
      mobile_phone:
        description: 手机号
        type: string
      nickname:
        description: 用户昵称
        type: string
    type: object
  define.OrderType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    format: int32
    type: integer
    x-enum-varnames:
    - OrderTypeNormal
    - OrderTypePresale
    - OrderTypeSeckill
    - OrderTypeNft
    - OrderTypeDraw
    - OrderTypeNftOfCoinStore
    - OrderTypeSteamItemOfCoinStore
    - OrderTypeDelivery
    - OrderTypeWcBonusOrder
  define.OrderUser:
    properties:
      _id:
        description: 用户ID
        type: string
      patbg_detail:
        allOf:
        - $ref: '#/definitions/define.OrderPatbgDetail'
        description: 用户详情
      type:
        description: 类型
        type: integer
    type: object
  define.PostSnapshot:
    properties:
      description:
        description: 帖子文案描述
        type: string
      media_files:
        description: 媒体文件数组
        items:
          $ref: '#/definitions/define.MediaFile'
        type: array
      price:
        description: 收购价格
        type: integer
    type: object
  define.ReceiveBonusReq:
    properties:
      is_all:
        description: 1-Yes 2-No
        type: integer
      user_bonus_log_id:
        type: integer
    type: object
  define.ReceiveBonusResp:
    type: object
  define.ReceiveStoryReq:
    properties:
      order_id:
        description: 故事玩法订单编号
        type: string
    required:
    - order_id
    type: object
  define.ReceiveStoryResp:
    properties:
      item_cover_url:
        description: 故事玩法物品图片(商品/组合商品)
        type: string
      item_title:
        description: 故事玩法物品名称(商品/组合商品)
        type: string
      qty:
        description: 集齐数量
        type: integer
    type: object
  define.Record:
    properties:
      area_code:
        description: 行政区域编码
        type: string
      area_name:
        description: 行政区域名称
        type: string
      context:
        description: 内容
        type: string
      ftime:
        description: 格式化后的时间
        type: string
      status:
        description: 状态
        type: string
      time:
        description: 时间
        type: string
    type: object
  define.RegisterDeviceReq:
    properties:
      device_id:
        description: 设备唯一标识符
        type: string
      device_model:
        description: 设备型号
        type: string
      os_version:
        description: 设备系统版本
        type: string
      platform:
        description: 设备平台
        enum:
        - ios
        - android
        type: string
      provider:
        description: 推送服务提供商
        enum:
        - jpush
        type: string
      push_id:
        description: 推送 id，极光传 registration_id
        type: string
    required:
    - device_id
    - platform
    - provider
    - push_id
    type: object
  define.RegisterDeviceResp:
    properties:
      created_at:
        type: string
      id:
        example: "0"
        type: string
    type: object
  define.ReviewMerchantApplicationReq:
    properties:
      approved:
        description: 是否通过
        type: boolean
      id:
        description: 申请ID
        type: string
    required:
    - approved
    - id
    type: object
  define.ReviewMerchantApplicationResp:
    properties:
      id:
        description: 申请ID
        type: string
    type: object
  define.SelectedStoryMaterialsData:
    properties:
      id:
        description: 故事玩法材料id
        type: integer
      materials_item_info:
        description: 故事玩法用户选的物品id集合
        items:
          $ref: '#/definitions/define.StoryDiscoveryUserMaterials'
        type: array
    type: object
  define.SendMessageReq:
    properties:
      client_msg_id:
        description: 客户端生成的消息ID，用于幂等性控制，（获取：请求/common/generate_id接口）
        type: string
      client_msg_number:
        description: 客户端生成序号，用于消息排序，（获取：每个会话的消息从1开始单调递增（0是智能回复）），后端排序逻辑：序号倒序排，相同或没有，按创建时间倒序排
        type: integer
      content:
        description: 消息内容
        type: string
      conversation_id:
        description: 会话ID
        type: string
      media:
        allOf:
        - $ref: '#/definitions/define.MediaFile'
        description: 媒体文件，可能为空
      message_type:
        allOf:
        - $ref: '#/definitions/enums.MessageType'
        description: 消息类型：1=文本 2=图片 3=帖子快照
        enum:
        - 1
        - 2
        - 3
      post_id:
        description: 帖子ID（帖子消息类型时必填）
        type: string
      post_snapshot:
        allOf:
        - $ref: '#/definitions/define.PostSnapshot'
        description: 帖子快照信息（帖子消息类型时必填）
      receiver_id:
        description: 接收者ID
        type: string
    required:
    - conversation_id
    - message_type
    - receiver_id
    type: object
  define.SendMessageResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 消息ID
        type: string
    type: object
  define.StoryDiscoveryReq:
    properties:
      activity_code:
        description: 活动编码
        type: string
      qty:
        description: 故事玩法数量
        minimum: 1
        type: integer
      story_materials:
        description: 选中故事玩法材料
        items:
          $ref: '#/definitions/define.SelectedStoryMaterialsData'
        type: array
    required:
    - activity_code
    - qty
    - story_materials
    type: object
  define.StoryDiscoveryResp:
    properties:
      end_time:
        description: 探索结束时间
        type: string
      item_cover_url:
        description: 故事玩法物品图片
        type: string
      item_id:
        description: 故事玩法物品id
        type: string
      item_title:
        description: 故事玩法物品名称
        type: string
      order_id:
        description: 订单号
        type: string
      qty:
        description: 故事玩法数量
        type: integer
    type: object
  define.StoryDiscoveryUserMaterials:
    properties:
      cost_price_order:
        description: 成本价排序顺序 1:升序 -1:降序
        type: integer
      item_id:
        description: 物品id
        type: string
      selected_all:
        description: 是否全选 【1-全选；2-不选】
        type: integer
      user_item_ids:
        description: 故事玩法材料物品id集合
        items:
          type: string
        type: array
    type: object
  define.StoryFinishReq:
    type: object
  define.StoryFinishResp:
    type: object
  define.StoryItemData:
    properties:
      image_url:
        description: 商品图片
        type: string
      item_name:
        description: 商品名称
        type: string
      price:
        description: 商品发行价
        type: integer
      usable_stock:
        description: 可用库存
        type: integer
    type: object
  define.StoryMaterialsAddData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      story_materials_data:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsData'
        type: array
    required:
    - materials_type
    - qty
    - story_materials_data
    type: object
  define.StoryMaterialsData:
    properties:
      destroy:
        description: 材料消耗【1-销毁；2-不销毁】
        type: integer
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品id
        type: string
      item_title:
        description: 商品名称
        type: string
      loop:
        description: 二次流转【1-开启；2-关闭】
        type: integer
      price:
        description: 商品发行价
        type: integer
      qty:
        description: 集齐数量
        maximum: 500
        type: integer
    required:
    - image_url
    - item_id
    - item_title
    - price
    - qty
    type: object
  define.StoryMaterialsDetailData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      story_materials_data:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsData'
        type: array
      story_materials_id:
        description: 故事玩法材料ID
        type: integer
    required:
    - materials_type
    - qty
    - story_materials_data
    type: object
  define.StoryMaterialsEditData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      story_materials_data:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.StoryMaterialsData'
        type: array
    required:
    - materials_type
    - qty
    - story_materials_data
    type: object
  define.StoryMaterialsGetReleaseTimeData:
    properties:
      release_time:
        type: integer
      user_item_id:
        type: string
    type: object
  define.StoryMaterialsGetReleaseTimeReq:
    properties:
      user_item_id:
        items:
          type: string
        type: array
    type: object
  define.StoryMaterialsGetReleaseTimeResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.StoryMaterialsGetReleaseTimeData'
        type: array
    type: object
  define.StoryMaterialsReleaseReq:
    properties:
      id:
        type: integer
    type: object
  define.StoryMaterialsReleaseResp:
    type: object
  define.StoryOrderCompleteReq:
    type: object
  define.StoryOrderCompleteResp:
    type: object
  define.StoryOrderUpChainReq:
    properties:
      order_id:
        type: string
    type: object
  define.StoryOrderUpChainResp:
    type: object
  define.StoryReleaseTime:
    properties:
      release_day:
        description: 故事玩法X天后释放
        type: integer
      release_time:
        description: 释放时间
        type: string
    type: object
  define.SubmitMerchantApplicationReq:
    type: object
  define.SubmitMerchantApplicationResp:
    properties:
      id:
        description: 申请ID
        type: string
    type: object
  define.SyncCirculationItemResp:
    type: object
  define.SynthesisFinishReq:
    type: object
  define.SynthesisFinishResp:
    type: object
  define.SynthesisMaterialsAddData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      synthesis_materials_data:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsData'
        type: array
    required:
    - materials_type
    - qty
    - synthesis_materials_data
    type: object
  define.SynthesisMaterialsData:
    properties:
      destroy:
        description: 材料消耗【1-销毁；2-不销毁】
        type: integer
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品id
        type: string
      item_title:
        description: 商品名称
        type: string
      loop:
        description: 二次流转【1-开启；2-关闭】
        type: integer
      price:
        description: 商品发行价
        type: integer
      qty:
        description: 集齐数量
        maximum: 500
        type: integer
    required:
    - image_url
    - item_id
    - item_title
    - price
    - qty
    type: object
  define.SynthesisMaterialsDetailData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      synthesis_materials_data:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsData'
        type: array
      synthesis_materials_id:
        description: 合成材料ID
        type: integer
    required:
    - materials_type
    - qty
    - synthesis_materials_data
    type: object
  define.SynthesisMaterialsEditData:
    properties:
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      synthesis_materials_data:
        description: 融合材料
        items:
          $ref: '#/definitions/define.SynthesisMaterialsData'
        type: array
    required:
    - materials_type
    - qty
    - synthesis_materials_data
    type: object
  define.SynthesisMaterialsGetReleaseTimeData:
    properties:
      release_time:
        type: integer
      user_item_id:
        type: string
    type: object
  define.SynthesisMaterialsGetReleaseTimeReq:
    properties:
      user_item_id:
        items:
          type: string
        type: array
    type: object
  define.SynthesisMaterialsGetReleaseTimeResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.SynthesisMaterialsGetReleaseTimeData'
        type: array
    type: object
  define.SynthesisMaterialsReleaseReq:
    properties:
      id:
        type: integer
    type: object
  define.SynthesisMaterialsReleaseResp:
    type: object
  define.SynthesisOrderUpChainReq:
    properties:
      order_id:
        type: string
    type: object
  define.SynthesisOrderUpChainResp:
    type: object
  define.SynthesisPrizeData:
    properties:
      image_url:
        description: 商品图片
        type: string
      item_name:
        description: 商品名称
        type: string
      price:
        description: 商品发行价
        type: integer
      usable_stock:
        description: 可用库存
        type: integer
    type: object
  define.SynthesisReleaseTime:
    properties:
      release_day:
        description: 融合X天后释放
        type: integer
      release_time:
        description: 释放时间
        type: string
    type: object
  define.TestPushReq:
    properties:
      apns_production:
        description: 是否为生产环境，iOS 专用
        type: boolean
      content:
        description: 推送内容
        type: string
      extras:
        additionalProperties: true
        description: '额外参数，如：{"url": "ojb://message_list?type=2"}'
        type: object
      push_ids:
        description: 推送 id 列表（极光推送填注册 id）
        items:
          type: string
        type: array
      title:
        description: 推送标题
        type: string
    required:
    - content
    - title
    type: object
  define.TestPushResp:
    properties:
      message_id:
        description: 消息 id
        type: string
    type: object
  define.ToggleSmartReplyReq:
    properties:
      enabled:
        description: 是否启用
        type: boolean
    type: object
  define.ToggleSmartReplyResp:
    properties:
      enabled:
        description: 是否启用
        type: boolean
    type: object
  define.TransactionAmountWebOverviewInfo:
    properties:
      amount:
        description: 历史累计成交额（单位：分）
        type: integer
      rate:
        description: 成交额百分比
        type: number
    type: object
  define.UpdateDeviceStatusReq:
    properties:
      device_id:
        description: 设备唯一标识符
        type: string
      status:
        description: 设备状态，1：在线，2：离线
        enum:
        - 1
        - 2
        type: integer
    required:
    - device_id
    - status
    type: object
  define.UpdateDeviceStatusResp:
    properties:
      id:
        example: "0"
        type: string
      updated_at:
        type: string
    type: object
  define.UpdateMarketOverviewResp:
    type: object
  define.UpdatePostStatusAdminReq:
    properties:
      id:
        description: 帖子ID
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
    required:
    - id
    - status
    type: object
  define.UpdatePostStatusAdminResp:
    properties:
      id:
        description: 帖子ID
        type: string
    type: object
  define.UpdatePostStatusReq:
    properties:
      id:
        description: 帖子ID
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enums.PostStatus'
        description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
    required:
    - id
    - status
    type: object
  define.UpdatePostStatusResp:
    properties:
      id:
        description: 帖子ID
        type: string
    type: object
  define.UpdateSmartReplyTemplateReq:
    properties:
      enabled:
        description: 是否启用
        type: boolean
      template_content:
        description: 模板内容，最多200字符
        maxLength: 200
        type: string
    required:
    - template_content
    type: object
  define.UpdateSmartReplyTemplateResp:
    properties:
      id:
        description: 模板ID
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  define.UserExchangeBonusItemReq:
    properties:
      address_id:
        description: 地址 id
        type: string
      bonus_item_id:
        description: 积分商品 id/活动 id
        example: "0"
        type: string
      exchange_qty:
        description: 兑换数量
        minimum: 1
        type: integer
    required:
    - address_id
    - bonus_item_id
    - exchange_qty
    type: object
  define.UserExchangeBonusItemResp:
    properties:
      exchange_time:
        type: string
      id:
        example: "0"
        type: string
    type: object
  define.UserInfo:
    properties:
      avatar:
        description: 用户头像
        type: string
      id:
        description: 用户ID
        type: string
      name:
        description: 用户名称
        type: string
    type: object
  define.UserItem:
    properties:
      _id:
        description: 文档ID（MongoDB建议使用primitive.ObjectID）
        type: string
      buy_price:
        description: 购买价格（单位：分）
        type: integer
      buy_time:
        description: 购买时间（RFC3339格式）
        type: string
      created_at:
        description: 记录创建时间
        type: string
      days_until_sellable:
        description: 可售倒计时（flow_status=5时生效）
        type: integer
      flow_status:
        allOf:
        - $ref: '#/definitions/define.FlowStatusEnum'
        description: 流转状态
      fusion_status:
        description: 融合状态【0:未融合;1:已融合】
        type: integer
      item_id:
        description: 物品唯一标识
        type: string
      receive_type:
        description: 获得方式（枚举值）
        type: integer
      story_status:
        description: 故事玩法派遣状态【0:未探索;1:已探索】
        type: integer
    type: object
  define.UserItemReceiveTypeEnum:
    enum:
    - 10
    - 124
    - 125
    - 126
    - 127
    format: int32
    type: integer
    x-enum-varnames:
    - UserItemReceiveTypeFantasyMartBuy
    - UserItemReceiveTypeIssueItem
    - UserItemReceiveTypeAirdrop
    - UserItemReceiveTypeSynthesis
    - UserItemReceiveTypeStory
  define.UserStoryMaterials:
    properties:
      id:
        description: 故事玩法材料id
        type: integer
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      story_materials_data:
        description: 故事玩法材料
        items:
          $ref: '#/definitions/define.UserStoryMaterialsData'
        type: array
    type: object
  define.UserStoryMaterialsData:
    properties:
      circulation_status:
        description: 流通状态1:不限流通;2:禁止流通
        type: integer
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品id
        type: string
      item_title:
        description: 商品名称
        type: string
      limit:
        allOf:
        - $ref: '#/definitions/define.UserStoryMaterialsLimitData'
        description: 物品限制信息
      qty:
        description: 集齐数量
        type: integer
      user_qty:
        description: 用户持有数量
        type: integer
    type: object
  define.UserStoryMaterialsLimitData:
    properties:
      day:
        description: x天
        type: integer
      status:
        description: 限制状态【1-派遣后销毁;2-限制流转，仅可提货;3-限制流转x天】
        type: integer
    type: object
  define.UserSynthesisMaterials:
    properties:
      id:
        description: 合成材料id
        type: integer
      materials_type:
        description: 材料类型【1-核心材料；2-关键材料】
        type: integer
      qty:
        description: 集齐数量
        type: integer
      synthesis_materials_data:
        description: 融合材料
        items:
          $ref: '#/definitions/define.UserSynthesisMaterialsData'
        type: array
    type: object
  define.UserSynthesisMaterialsData:
    properties:
      circulation_status:
        description: 流通状态1:不限流通;2:禁止流通
        type: integer
      image_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品id
        type: string
      item_title:
        description: 商品名称
        type: string
      qty:
        description: 集齐数量
        type: integer
      user_qty:
        description: 用户持有数量
        type: integer
    type: object
  enum.CategoryRelateType:
    enum:
    - 1
    format: int32
    type: integer
    x-enum-comments:
      CategoryRelateTypeMarketChanges: 行情异动
    x-enum-descriptions:
    - 行情异动
    x-enum-varnames:
    - CategoryRelateTypeMarketChanges
  enum.CategoryStatus:
    enum:
    - -1
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      CategoryStatusDeleted: 已删除
      CategoryStatusDisable: 禁用
      CategoryStatusEnable: 启用
    x-enum-descriptions:
    - 已删除
    - 启用
    - 禁用
    x-enum-varnames:
    - CategoryStatusDeleted
    - CategoryStatusEnable
    - CategoryStatusDisable
  enum.OperationLogActionEnum:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    format: int32
    type: integer
    x-enum-comments:
      OperationLogActionCreate: 创建
      OperationLogActionDelete: 删除
      OperationLogActionPublish: 发布
      OperationLogActionRemove: 下架
      OperationLogActionSyncMediaOFF: 同步媒体-关
      OperationLogActionSyncMediaON: 同步媒体-开
      OperationLogActionUpdate: 更新
    x-enum-descriptions:
    - 创建
    - 更新
    - 删除
    - 发布
    - 下架
    - 同步媒体-开
    - 同步媒体-关
    x-enum-varnames:
    - OperationLogActionCreate
    - OperationLogActionUpdate
    - OperationLogActionDelete
    - OperationLogActionPublish
    - OperationLogActionRemove
    - OperationLogActionSyncMediaON
    - OperationLogActionSyncMediaOFF
  enums.AnnCategoryStatus:
    enum:
    - -1
    - 1
    format: int32
    type: integer
    x-enum-comments:
      AnnCategoryStatusDeleted: 已删除
      AnnCategoryStatusEnable: 启用
    x-enum-descriptions:
    - 已删除
    - 启用
    x-enum-varnames:
    - AnnCategoryStatusDeleted
    - AnnCategoryStatusEnable
  enums.AnnouncementMessagePushEnum:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      AnnouncementMessagePushNo: 不推送
      AnnouncementMessagePushYes: 推送
    x-enum-descriptions:
    - 推送
    - 不推送
    x-enum-varnames:
    - AnnouncementMessagePushYes
    - AnnouncementMessagePushNo
  enums.AnnouncementPublishType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - AnnouncementPublishTypeImmediate
    - AnnouncementPublishTypeTiming
  enums.AnnouncementStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    format: int32
    type: integer
    x-enum-varnames:
    - AnnouncementStatusDraft
    - AnnouncementStatusScheduled
    - AnnouncementStatusPublished
    - AnnouncementStatusOffline
  enums.AnnouncementTimeType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - AnnouncementTimeTypePublish
    - AnnouncementTimeTypeCreate
  enums.ApplicationStatus:
    enum:
    - -1
    - 1
    - 2
    type: integer
    x-enum-comments:
      ApplicationStatusApproved: 审核通过
      ApplicationStatusPending: 待审核
      ApplicationStatusRejected: 审核不通过
    x-enum-descriptions:
    - 审核不通过
    - 待审核
    - 审核通过
    x-enum-varnames:
    - ApplicationStatusRejected
    - ApplicationStatusPending
    - ApplicationStatusApproved
  enums.DateType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      DateTypeCreateTime: 创建时间
      DateTypeLastMessageTime: 最近消息时间
    x-enum-descriptions:
    - 创建时间
    - 最近消息时间
    x-enum-varnames:
    - DateTypeCreateTime
    - DateTypeLastMessageTime
  enums.MarketChangesMessagePushType:
    enum:
    - 0
    - 1
    format: int32
    type: integer
    x-enum-varnames:
    - MarketChangesMessagePushNo
    - MarketChangesMessagePushYes
  enums.MarketChangesPublishType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - MarketChangesPublishTypeImmediate
    - MarketChangesPublishTypeTiming
  enums.MarketChangesStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    format: int32
    type: integer
    x-enum-varnames:
    - MarketChangesStatusDraft
    - MarketChangesStatusScheduled
    - MarketChangesStatusPublished
    - MarketChangesStatusOffline
  enums.MarketChangesTimeType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - MarketChangesTimeTypePublish
    - MarketChangesTimeTypeCreate
  enums.MediaType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      MediaTypeImage: 图片
      MediaTypeVideo: 视频
    x-enum-descriptions:
    - 图片
    - 视频
    x-enum-varnames:
    - MediaTypeImage
    - MediaTypeVideo
  enums.MessageType:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      MessageTypeImage: 图片
      MessageTypePost: 帖子快照
      MessageTypeText: 文本
    x-enum-descriptions:
    - 文本
    - 图片
    - 帖子快照
    x-enum-varnames:
    - MessageTypeText
    - MessageTypeImage
    - MessageTypePost
  enums.OperationAnnCategoryStatus:
    enum:
    - -1
    - 1
    format: int32
    type: integer
    x-enum-comments:
      OperationAnnCategoryStatusDeleted: 已删除
      OperationAnnCategoryStatusEnable: 启用
    x-enum-descriptions:
    - 已删除
    - 启用
    x-enum-varnames:
    - OperationAnnCategoryStatusDeleted
    - OperationAnnCategoryStatusEnable
  enums.OperationAnnouncementMessagePushEnum:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      OperationAnnouncementMessagePushNo: 不推送
      OperationAnnouncementMessagePushYes: 推送
    x-enum-descriptions:
    - 推送
    - 不推送
    x-enum-varnames:
    - OperationAnnouncementMessagePushYes
    - OperationAnnouncementMessagePushNo
  enums.OperationAnnouncementPublishType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - OperationAnnouncementPublishTypeImmediate
    - OperationAnnouncementPublishTypeTiming
  enums.OperationAnnouncementStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    format: int32
    type: integer
    x-enum-varnames:
    - OperationAnnouncementStatusDraft
    - OperationAnnouncementStatusScheduled
    - OperationAnnouncementStatusPublished
    - OperationAnnouncementStatusOffline
  enums.OperationAnnouncementTimeType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - OperationAnnouncementTimeTypePublish
    - OperationAnnouncementTimeTypeCreate
  enums.OrderType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - OrderTypeYcOpen
    - OrderTypeWc
  enums.PostStatus:
    enum:
    - -3
    - -2
    - -1
    - 1
    type: integer
    x-enum-comments:
      PostStatusActive: 已上架
      PostStatusDeleted: 已删除
      PostStatusInactive: 已下架
      PostStatusViolation: 违规下架
    x-enum-descriptions:
    - 违规下架
    - 已下架
    - 已删除
    - 已上架
    x-enum-varnames:
    - PostStatusViolation
    - PostStatusInactive
    - PostStatusDeleted
    - PostStatusActive
  enums.WithdrawType:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-varnames:
    - WithdrawTypeManual
    - WithdrawTypeForced
  model.UserLoginLog:
    properties:
      address:
        description: 地址信息(JSON格式)
        items:
          type: integer
        type: array
      created_at:
        description: 创建时间
        type: string
      id:
        description: Id
        type: string
      login_ip:
        description: 登录IP
        type: string
      login_time:
        description: 登录时间
        type: string
      type:
        description: 登录类型
        type: string
      updated_at:
        description: 更新时间
        type: string
      user_id:
        description: 用户ID
        type: string
      version:
        description: 版本号
        type: string
    type: object
  mongdb.CirculationStatusEnum:
    enum:
    - 2
    - 1
    format: int32
    type: integer
    x-enum-comments:
      CirculationStatusAllow: 允许流通
      CirculationStatusProhibit: 禁止流通
    x-enum-descriptions:
    - 禁止流通
    - 允许流通
    x-enum-varnames:
    - CirculationStatusProhibit
    - CirculationStatusAllow
  mongdb.IssueItemStatusEnum:
    enum:
    - -1
    - 0
    - 1
    format: int32
    type: integer
    x-enum-comments:
      IssueItemStatusInitial: 未上架
      IssueItemStatusOffSale: 已下架
      IssueItemStatusOnSale: 已上架
    x-enum-descriptions:
    - 未上架
    - 已下架
    - 已上架
    x-enum-varnames:
    - IssueItemStatusInitial
    - IssueItemStatusOffSale
    - IssueItemStatusOnSale
  mongdb.IssueItemStoryStatusEnum:
    enum:
    - 0
    - 1
    format: int32
    type: integer
    x-enum-comments:
      IssueItemStoryStatusClose: 关闭
      IssueItemStoryStatusOpen: 开启
    x-enum-descriptions:
    - 关闭
    - 开启
    x-enum-varnames:
    - IssueItemStoryStatusClose
    - IssueItemStoryStatusOpen
  mongdb.IssueItemSynthesisStatusEnum:
    enum:
    - 0
    - 1
    format: int32
    type: integer
    x-enum-comments:
      IssueItemSynthesisStatusClose: 关闭
      IssueItemSynthesisStatusOpen: 开启
    x-enum-descriptions:
    - 关闭
    - 开启
    x-enum-varnames:
    - IssueItemSynthesisStatusClose
    - IssueItemSynthesisStatusOpen
  pat.CheckAdmJwtUserInfo:
    properties:
      _id:
        type: string
      mobile_phone:
        type: string
      name:
        type: string
      real_name:
        type: string
    type: object
  response.Data:
    properties:
      code:
        type: integer
      data: {}
      desc:
        type: string
      trace_id:
        description: 链路id
        type: string
    type: object
  yc_open.GetUserItemsByItemIdListResItem:
    properties:
      _id:
        type: string
      item_id:
        type: string
      open_user_id:
        type: string
    type: object
  yc_open.OrderAddress:
    properties:
      area:
        description: 所在地区
        type: string
      code:
        description: 地区编码
        type: string
      mobile_phone:
        description: 收件人手机号
        type: string
      name:
        description: 收件人姓名
        type: string
      place:
        description: 详细地址
        type: string
      remark:
        description: 备注
        type: string
    type: object
  yc_open.OrderExtends:
    properties:
      address:
        allOf:
        - $ref: '#/definitions/yc_open.OrderAddress'
        description: AddressInfo            string              `json:"address_info"`              //
          地址信息(JSON字符串)
      address_id:
        description: Remark     string `json:"remark"`       // 备注
        type: string
      admin_sale_freight_amount:
        description: 管理员调整运费
        type: integer
      appid:
        description: 商品类型
        type: integer
      balance_due_amount:
        description: 尾款
        type: integer
      coupon_amount:
        description: 优惠金额
        type: integer
      coupon_id:
        description: 使用的优惠券ID
        type: string
      deposit_amount:
        description: 定金
        type: integer
      deposit_recharge_order_id:
        description: 定金支付订单ID
        type: string
      deposit_recharge_time:
        description: 定金支付时间
        type: string
      freight_amount:
        description: 运费
        type: integer
      is_buy_order:
        description: 是否为直购商品
        type: boolean
      items:
        description: 商品列表
        items:
          $ref: '#/definitions/yc_open.OrderExtendsItems'
        type: array
      order_type:
        allOf:
        - $ref: '#/definitions/define.OrderType'
        description: 订单类型
      recharge_order_id:
        description: 充值订单ID【尾款充值订单】
        type: string
      recharge_time:
        description: 充值时间【尾款支付时间】
        type: string
      remind_mobile_phone:
        description: 订阅提醒手机号码
        type: string
    type: object
  yc_open.OrderExtendsItems:
    properties:
      freight_check_time:
        description: 签收时间
        type: string
      freight_no:
        description: 物流单号
        type: string
      freight_time:
        description: 发货时间
        type: string
      item_id:
        description: 商品ID
        type: string
      mall_item_id:
        description: 商品ID（从直购商城取回）
        type: string
      pay_amount:
        description: 实付金额
        type: integer
      quantity:
        description: 商品数
        type: integer
      receive_type:
        $ref: '#/definitions/define.UserItemReceiveTypeEnum'
      status:
        allOf:
        - $ref: '#/definitions/define.ItemWithdrawOrderStatus'
        description: 子订单状态
      user_item_ids:
        description: UserItemID string `json:"user_item_id"` // 用户背包ID（从用户背包取回）
        items:
          type: string
        type: array
    type: object
  yc_open.OrderLogsData:
    properties:
      list:
        description: 日志列表
        items: {}
        type: array
      total:
        description: 日志总数
        type: integer
    type: object
  yc_open.UpdateExtends:
    properties:
      address:
        allOf:
        - $ref: '#/definitions/yc_open.OrderAddress'
        description: AddressInfo   string       `json:"address_info"`   // 地址信息(JSON字符串)：例："{\"name\":\"airmart\",\"mobile_phone\":\"15818446166\",\"code\":\"230404\",\"area\":\"广东省|深圳市|南山区\",\"place\":\"北邮科技大厦11楼\"}"
      freight_amount:
        description: 邮费
        type: integer
    type: object
info:
  contact: {}
  description: 接口文档
  title: app API
  version: 1.0.0
paths:
  /admin/v1/announcement/add:
    post:
      description: 新增公告
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 新增公告
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/category/add:
    post:
      description: 新增公告分类
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 新增公告分类
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/category/del:
    post:
      description: 公告分类删除
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 公告分类删除
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/category/detail:
    get:
      description: 查询公告分类详情
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAnnCategoryAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询公告分类详情
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/category/edit:
    post:
      description: 编辑公告分类
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑公告分类
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/category/edit_priority:
    post:
      description: 公告分类优先级编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnCategoryPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnCategoryPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 公告分类优先级编辑
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/category/list:
    get:
      description: 查询公告分类列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAnnCategoryAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询公告分类列表
      tags:
      - 管理端-平台方公告分类管理
  /admin/v1/announcement/del:
    post:
      description: 公告删除
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 公告删除
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/detail:
    get:
      description: 查询公告详情
      parameters:
      - description: 公告ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAnnouncementAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询公告详情
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/edit:
    post:
      description: 编辑公告
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑公告
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/edit_ad_ids:
    post:
      description: 公告关联广告ID编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnouncementAdIdsReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnouncementAdIdsResp'
              type: object
      security:
      - Bearer: []
      summary: 公告关联广告ID编辑
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/edit_priority:
    post:
      description: 公告优先级编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnouncementPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnouncementPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 公告优先级编辑
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/edit_status:
    post:
      description: 公告状态编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditAnnouncementStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditAnnouncementStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 公告状态编辑
      tags:
      - 管理端-平台方公告管理
  /admin/v1/announcement/list:
    get:
      description: 查询公告列表
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 渠道ID
        in: query
        name: channel_id
        type: string
      - description: 创建人
        in: query
        name: created_by
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 公告ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      - description: 状态
        enum:
        - 1
        - 2
        - 3
        - 4
        format: int32
        in: query
        name: status
        type: integer
        x-enum-varnames:
        - AnnouncementStatusDraft
        - AnnouncementStatusScheduled
        - AnnouncementStatusPublished
        - AnnouncementStatusOffline
      - description: 时间类型
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: time_type
        type: integer
        x-enum-varnames:
        - AnnouncementTimeTypePublish
        - AnnouncementTimeTypeCreate
      - description: 公告标题
        in: query
        name: title
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAnnouncementAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询公告列表
      tags:
      - 管理端-平台方公告管理
  /admin/v1/bonus_mall/bonus_item/add:
    post:
      description: 新增积分商品
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddBonusItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddBonusItemResp'
              type: object
      security:
      - Bearer: []
      summary: 新增积分商品
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/bonus_item/detail:
    get:
      description: 获取积分商品详情
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetBonusItemDetailAdminReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetBonusItemDetailAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 获取积分商品详情
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/bonus_item/edit:
    post:
      description: 修改积分商品
      parameters:
      - description: 修改参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditBonusItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditBonusItemResp'
              type: object
      security:
      - Bearer: []
      summary: 修改积分商品
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/bonus_item/edit_status:
    post:
      description: 修改积分商品状态
      parameters:
      - description: 修改参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditBonusItemStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditBonusItemStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 修改积分商品状态
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/bonus_item/list:
    get:
      description: 获取积分商品列表
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetBonusItemListAdminReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetBonusItemListAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 获取积分商品列表
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/exchange_log/list:
    get:
      description: 获取积分商品兑换记录列表
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetExchangeLogListAdminReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetExchangeLogListAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 获取积分商品兑换记录列表
      tags:
      - 管理端-积分商城管理
  /admin/v1/bonus_mall/exchange_log/list_export:
    get:
      description: 导出积分商品兑换记录列表
      parameters:
      - in: query
        name: end_time
        type: string
      - description: 积分商品 id/活动 id
        example: "0"
        in: query
        name: bonus_item_id
        type: string
      - description: 商品搜索关键词
        in: query
        name: item_keyword
        type: string
      - description: 用户手机号
        in: query
        name: mobile_phone
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 商品 sku
        in: query
        name: sku_no
        type: string
      - in: query
        name: start_time
        type: string
      - description: 用户 id
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 导出积分商品兑换记录列表
      tags:
      - 管理端-积分商城管理
  /admin/v1/common/category/add:
    post:
      description: 新增分类
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 新增分类
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/del:
    post:
      description: 分类删除
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 分类删除
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/detail:
    get:
      description: 查询分类详情
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCategoryAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询分类详情
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/edit:
    post:
      description: 编辑分类
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑分类
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/edit_priority:
    post:
      description: 分类优先级编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditCategoryPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditCategoryPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 分类优先级编辑
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/edit_status:
    post:
      description: 编辑分类状态
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditCategoryStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditCategoryStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑分类状态
      tags:
      - 管理端-分类管理
  /admin/v1/common/category/list:
    get:
      description: 查询分类列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 关联类型
        enum:
        - 1
        format: int32
        in: query
        name: relate_type
        required: true
        type: integer
        x-enum-comments:
          CategoryRelateTypeMarketChanges: 行情异动
        x-enum-descriptions:
        - 行情异动
        x-enum-varnames:
        - CategoryRelateTypeMarketChanges
      - description: 状态
        enum:
        - -1
        - 1
        - 2
        format: int32
        in: query
        name: status
        type: integer
        x-enum-comments:
          CategoryStatusDeleted: 已删除
          CategoryStatusDisable: 禁用
          CategoryStatusEnable: 启用
        x-enum-descriptions:
        - 已删除
        - 启用
        - 禁用
        x-enum-varnames:
        - CategoryStatusDeleted
        - CategoryStatusEnable
        - CategoryStatusDisable
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCategoryAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询分类列表
      tags:
      - 管理端-分类管理
  /admin/v1/common/operation_log/list:
    get:
      description: 查询操作日志列表
      parameters:
      - in: query
        name: relate_id
        required: true
        type: integer
      - in: query
        name: relate_type
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetOperationLogListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询操作日志列表
      tags:
      - 管理端-操作日志
  /admin/v1/conversations/detail:
    get:
      description: 管理员查看会话详情
      parameters:
      - description: 会话记录ID
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetConversationAdminDetailResp'
      summary: 获取会话详情（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/会话管理
  /admin/v1/conversations/list:
    get:
      description: 管理员查看会话列表，支持多条件筛选
      parameters:
      - description: 客户用户ID
        in: query
        name: client_user_id
        type: string
      - description: 日期类型 1:创建时间,2:最近消息时间
        enum:
        - 1
        - 2
        in: query
        name: date_type
        type: integer
        x-enum-comments:
          DateTypeCreateTime: 创建时间
          DateTypeLastMessageTime: 最近消息时间
        x-enum-descriptions:
        - 创建时间
        - 最近消息时间
        x-enum-varnames:
        - DateTypeCreateTime
        - DateTypeLastMessageTime
      - description: 时间结束
        in: query
        name: end_time
        type: string
      - description: 会话记录ID
        in: query
        name: id
        type: string
      - description: 关键字（搜索消息内容）
        in: query
        name: keyword
        type: string
      - description: 商家用户ID
        in: query
        name: merchant_user_id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 帖子ID
        in: query
        name: post_id
        type: string
      - description: 时间开始
        in: query
        name: start_time
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetConversationAdminListResp'
      summary: 获取会话列表（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/会话管理
  /admin/v1/conversations/messages/list:
    get:
      description: 管理员查看指定会话的消息列表，支持分页
      parameters:
      - description: 会话ID
        in: query
        name: conversation_id
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetMessageAdminListResp'
      summary: 获取消息列表（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/消息管理
  /admin/v1/invite_reward/edit_invite_config:
    post:
      description: 编辑邀友配置
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditInviteConfigAdminReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditInviteConfigAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑邀友配置
      tags:
      - 管理端
  /admin/v1/invite_reward/get_invite_config:
    get:
      description: 查询邀友奖励配置
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.InviteRewardConfig'
              type: object
      security:
      - Bearer: []
      summary: 查询邀友奖励配置
      tags:
      - 管理端
  /admin/v1/invite_reward/invite_admin_list:
    get:
      description: 获取用户邀友管理列表
      parameters:
      - in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - in: query
        name: purchase_time_end
        type: string
      - in: query
        name: purchase_time_start
        type: string
      - in: query
        name: reg_time_end
        type: string
      - in: query
        name: reg_time_start
        type: string
      - in: query
        name: target_keyword
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetInviteAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户邀友管理列表
      tags:
      - 管理端
  /admin/v1/invite_reward/invite_admin_list_export:
    get:
      description: 导出用户邀友管理列表
      parameters:
      - in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - in: query
        name: purchase_time_end
        type: string
      - in: query
        name: purchase_time_start
        type: string
      - in: query
        name: reg_time_end
        type: string
      - in: query
        name: reg_time_start
        type: string
      - in: query
        name: target_keyword
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 导出用户邀友管理列表
      tags:
      - 管理端
  /admin/v1/market_changes/add:
    post:
      description: 新增行情异动
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddMarketChangesReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddMarketChangesResp'
              type: object
      security:
      - Bearer: []
      summary: 新增行情异动
      tags:
      - 管理端-行情异动管理
  /admin/v1/market_changes/detail:
    get:
      description: 查询行情异动详情
      parameters:
      - description: 行情异动ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketChangesAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询行情异动详情
      tags:
      - 管理端-行情异动管理
  /admin/v1/market_changes/edit:
    post:
      description: 编辑行情异动
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMarketChangesReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMarketChangesResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑行情异动
      tags:
      - 管理端-行情异动管理
  /admin/v1/market_changes/edit_status:
    post:
      description: 行情异动状态编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMarketChangesStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMarketChangesStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 行情异动状态编辑
      tags:
      - 管理端-行情异动管理
  /admin/v1/market_changes/list:
    get:
      description: 查询行情异动列表
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 渠道ID
        in: query
        name: channel_id
        type: string
      - description: 创建人
        in: query
        name: created_by
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 行情异动ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      - description: 状态
        enum:
        - 1
        - 2
        - 3
        - 4
        format: int32
        in: query
        name: status
        type: integer
        x-enum-varnames:
        - MarketChangesStatusDraft
        - MarketChangesStatusScheduled
        - MarketChangesStatusPublished
        - MarketChangesStatusOffline
      - description: 时间类型
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: time_type
        type: integer
        x-enum-varnames:
        - MarketChangesTimeTypePublish
        - MarketChangesTimeTypeCreate
      - description: 行情异动标题
        in: query
        name: title
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketChangesAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询行情异动列表
      tags:
      - 管理端-行情异动管理
  /admin/v1/merchant_applications/list:
    get:
      description: 管理员查看商家申请列表，支持多条件筛选
      parameters:
      - description: 申请时间结束
        in: query
        name: end_time
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 申请时间开始
        in: query
        name: start_time
        type: string
      - description: 申请状态：-1=审核不通过 1=待审核 2=审核通过
        enum:
        - -1
        - 1
        - 2
        in: query
        name: status
        type: integer
        x-enum-comments:
          ApplicationStatusApproved: 审核通过
          ApplicationStatusPending: 待审核
          ApplicationStatusRejected: 审核不通过
        x-enum-descriptions:
        - 审核不通过
        - 待审核
        - 审核通过
        x-enum-varnames:
        - ApplicationStatusRejected
        - ApplicationStatusPending
        - ApplicationStatusApproved
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetMerchantApplicationAdminListResp'
      summary: 获取商家申请列表（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/商家管理
  /admin/v1/merchant_applications/review:
    post:
      description: 管理员审核商家申请，通过或拒绝
      parameters:
      - description: 审核参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ReviewMerchantApplicationReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.ReviewMerchantApplicationResp'
      summary: 审核商家申请（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/商家管理
  /admin/v1/operation_announcement/add:
    post:
      description: 新增运营公告
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddOperationAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddOperationAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 新增运营公告
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/category/add:
    post:
      description: 新增运营公告分类
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddOperationAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddOperationAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 新增运营公告分类
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/category/del:
    post:
      description: 运营公告分类删除
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelOperationAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelOperationAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告分类删除
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/category/detail:
    get:
      description: 查询运营公告分类详情
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetOperationAnnCategoryAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询运营公告分类详情
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/category/edit:
    post:
      description: 编辑运营公告分类
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnCategoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnCategoryResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑运营公告分类
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/category/edit_priority:
    post:
      description: 运营公告分类优先级编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnCategoryPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnCategoryPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告分类优先级编辑
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/category/list:
    get:
      description: 查询运营公告分类列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetOperationAnnCategoryAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询运营公告分类列表
      tags:
      - 管理端-运营方公告分类管理
  /admin/v1/operation_announcement/del:
    post:
      description: 运营公告删除
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelOperationAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelOperationAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告删除
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/detail:
    get:
      description: 查询运营公告详情
      parameters:
      - description: 运营公告ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetOperationAnnouncementAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询运营公告详情
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/edit:
    post:
      description: 编辑运营公告
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnouncementReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnouncementResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑运营公告
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/edit_ad_ids:
    post:
      description: 运营公告关联广告ID编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnouncementAdIdsReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnouncementAdIdsResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告关联广告ID编辑
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/edit_priority:
    post:
      description: 运营公告优先级编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnouncementPriorityReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnouncementPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告优先级编辑
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/edit_status:
    post:
      description: 运营公告状态编辑
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditOperationAnnouncementStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditOperationAnnouncementStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 运营公告状态编辑
      tags:
      - 管理端-运营方公告管理
  /admin/v1/operation_announcement/list:
    get:
      description: 查询运营公告列表
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 渠道ID
        in: query
        name: channel_id
        type: string
      - description: 创建人
        in: query
        name: created_by
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 运营公告ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      - description: 状态
        enum:
        - 1
        - 2
        - 3
        - 4
        format: int32
        in: query
        name: status
        type: integer
        x-enum-varnames:
        - OperationAnnouncementStatusDraft
        - OperationAnnouncementStatusScheduled
        - OperationAnnouncementStatusPublished
        - OperationAnnouncementStatusOffline
      - description: 时间类型
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: time_type
        type: integer
        x-enum-varnames:
        - OperationAnnouncementTimeTypePublish
        - OperationAnnouncementTimeTypeCreate
      - description: 运营公告标题
        in: query
        name: title
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetOperationAnnouncementAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询运营公告列表
      tags:
      - 管理端-运营方公告管理
  /admin/v1/posts/detail:
    get:
      description: 管理员查看求购帖子详细信息
      parameters:
      - description: 帖子ID
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetPostAdminDetailResp'
      summary: 获取求购帖子详情（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/求购管理
  /admin/v1/posts/edit:
    post:
      description: 管理员更新帖子状态，如违规下架
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdatePostStatusAdminReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.UpdatePostStatusAdminResp'
      summary: 更新帖子状态（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/求购管理
  /admin/v1/posts/list:
    get:
      description: 管理员查看求购帖子列表，支持多条件筛选
      parameters:
      - description: 创建时间结束
        in: query
        name: end_time
        type: string
      - description: 关键字(搜索描述)
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 帖子ID
        in: query
        name: post_id
        type: string
      - description: 创建时间开始
        in: query
        name: start_time
        type: string
      - description: 帖子状态筛选：-3=违规下架 -2=已下架 -1=已删除 1=已上架
        enum:
        - -3
        - -2
        - -1
        - 1
        in: query
        name: status
        type: integer
        x-enum-comments:
          PostStatusActive: 已上架
          PostStatusDeleted: 已删除
          PostStatusInactive: 已下架
          PostStatusViolation: 违规下架
        x-enum-descriptions:
        - 违规下架
        - 已下架
        - 已删除
        - 已上架
        x-enum-varnames:
        - PostStatusViolation
        - PostStatusInactive
        - PostStatusDeleted
        - PostStatusActive
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetPostAdminListResp'
      summary: 获取求购帖子列表（管理端）
      tags:
      - 管理端-卡牌集社
      x-apifox-folder: 管理端/求购管理
  /admin/v1/ship_manage/detail:
    get:
      description: 查询发货管理详情
      parameters:
      - description: 发货管理ID
        in: query
        name: yc_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetShipManageAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询发货管理详情
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/edit:
    put:
      description: 编辑发货管理
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditShipManageReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditShipManageResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑发货管理
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/forced_shipment_batch:
    post:
      description: 批量强制发货
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/app_service_apps_business_yc_define.ForcedShipmentBatchAdminReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/app_service_apps_business_yc_define.ForcedShipmentBatchAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 批量强制发货
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/freight_sync:
    post:
      description: 同步物流信息
      parameters:
      - description: 同步参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.FreightSyncReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.FreightSyncResp'
              type: object
      security:
      - Bearer: []
      summary: 同步物流信息
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/list:
    get:
      description: 查询发货管理列表
      parameters:
      - description: 提货时间
        in: query
        name: created_at_end
        type: string
      - description: 提货时间
        in: query
        name: created_at_start
        type: string
      - description: 快递单号
        in: query
        name: freight_no
        type: string
      - description: 发货时间
        in: query
        name: freight_time_end
        type: string
      - description: 发货时间
        in: query
        name: freight_time_start
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 商品状态
        enum:
        - -2
        - -1
        - 0
        - 1
        - 2
        - 3
        - 4
        - 10
        - 20
        - 30
        - 35
        - 36
        - 39
        - 40
        - 50
        - 60
        - 61
        format: int32
        in: query
        name: items_status
        type: integer
        x-enum-varnames:
        - WithdrawOrderStatusAbandoned
        - WithdrawOrderStatusDeleted
        - WithdrawOrderStatusExpired
        - WithdrawOrderStatusDone
        - WithdrawOrderStatusAftersaleing
        - WithdrawOrderStatusAfterSaleRefunding
        - WithdrawOrderStatusAfterSaleDone
        - WithdrawOrderStatusCreated
        - WithdrawOrderStatusLocked
        - WithdrawOrderStatusChecked
        - WithdrawOrderStatusWaitForPay
        - WithdrawOrderStatusWaitForShip
        - WithdrawOrderStatusPartShiped
        - WithdrawOrderStatusShiped
        - WithdrawOrderStatusReceived
        - WithdrawOrderStatusWaitForMake
        - WithdrawOrderStatusWaitForDue
      - description: 手机号
        in: query
        name: mobile_phone
        type: string
      - description: 订单来源：1：云仓提货:2：文潮提货。
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: order_type
        type: integer
        x-enum-varnames:
        - OrderTypeYcOpen
        - OrderTypeWc
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 发货管理ID
        example: "0"
        in: query
        name: ship_manage_id
        type: string
      - description: 订单状态
        enum:
        - -2
        - -1
        - 0
        - 1
        - 2
        - 3
        - 4
        - 10
        - 20
        - 30
        - 35
        - 36
        - 39
        - 40
        - 50
        - 60
        - 61
        format: int32
        in: query
        name: status
        type: integer
        x-enum-varnames:
        - WithdrawOrderStatusAbandoned
        - WithdrawOrderStatusDeleted
        - WithdrawOrderStatusExpired
        - WithdrawOrderStatusDone
        - WithdrawOrderStatusAftersaleing
        - WithdrawOrderStatusAfterSaleRefunding
        - WithdrawOrderStatusAfterSaleDone
        - WithdrawOrderStatusCreated
        - WithdrawOrderStatusLocked
        - WithdrawOrderStatusChecked
        - WithdrawOrderStatusWaitForPay
        - WithdrawOrderStatusWaitForShip
        - WithdrawOrderStatusPartShiped
        - WithdrawOrderStatusShiped
        - WithdrawOrderStatusReceived
        - WithdrawOrderStatusWaitForMake
        - WithdrawOrderStatusWaitForDue
      - description: 用户id
        in: query
        name: user_id
        type: string
      - description: '提货方式：1：手动提货: 2：强制发货。'
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: withdraw_type
        type: integer
        x-enum-varnames:
        - WithdrawTypeManual
        - WithdrawTypeForced
      - description: 云仓id
        in: query
        name: yc_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetShipManageAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询发货管理列表
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/list_export:
    get:
      description: 导出发货管理列表
      parameters:
      - description: 提货时间
        in: query
        name: created_at_end
        type: string
      - description: 提货时间
        in: query
        name: created_at_start
        type: string
      - description: 快递单号
        in: query
        name: freight_no
        type: string
      - description: 发货时间
        in: query
        name: freight_time_end
        type: string
      - description: 发货时间
        in: query
        name: freight_time_start
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 商品状态
        enum:
        - -2
        - -1
        - 0
        - 1
        - 2
        - 3
        - 4
        - 10
        - 20
        - 30
        - 35
        - 36
        - 39
        - 40
        - 50
        - 60
        - 61
        format: int32
        in: query
        name: items_status
        type: integer
        x-enum-varnames:
        - WithdrawOrderStatusAbandoned
        - WithdrawOrderStatusDeleted
        - WithdrawOrderStatusExpired
        - WithdrawOrderStatusDone
        - WithdrawOrderStatusAftersaleing
        - WithdrawOrderStatusAfterSaleRefunding
        - WithdrawOrderStatusAfterSaleDone
        - WithdrawOrderStatusCreated
        - WithdrawOrderStatusLocked
        - WithdrawOrderStatusChecked
        - WithdrawOrderStatusWaitForPay
        - WithdrawOrderStatusWaitForShip
        - WithdrawOrderStatusPartShiped
        - WithdrawOrderStatusShiped
        - WithdrawOrderStatusReceived
        - WithdrawOrderStatusWaitForMake
        - WithdrawOrderStatusWaitForDue
      - description: 手机号
        in: query
        name: mobile_phone
        type: string
      - description: 订单来源：1：云仓提货:2：文潮提货。
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: order_type
        type: integer
        x-enum-varnames:
        - OrderTypeYcOpen
        - OrderTypeWc
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 发货管理ID
        example: "0"
        in: query
        name: ship_manage_id
        type: string
      - description: 订单状态
        enum:
        - -2
        - -1
        - 0
        - 1
        - 2
        - 3
        - 4
        - 10
        - 20
        - 30
        - 35
        - 36
        - 39
        - 40
        - 50
        - 60
        - 61
        format: int32
        in: query
        name: status
        type: integer
        x-enum-varnames:
        - WithdrawOrderStatusAbandoned
        - WithdrawOrderStatusDeleted
        - WithdrawOrderStatusExpired
        - WithdrawOrderStatusDone
        - WithdrawOrderStatusAftersaleing
        - WithdrawOrderStatusAfterSaleRefunding
        - WithdrawOrderStatusAfterSaleDone
        - WithdrawOrderStatusCreated
        - WithdrawOrderStatusLocked
        - WithdrawOrderStatusChecked
        - WithdrawOrderStatusWaitForPay
        - WithdrawOrderStatusWaitForShip
        - WithdrawOrderStatusPartShiped
        - WithdrawOrderStatusShiped
        - WithdrawOrderStatusReceived
        - WithdrawOrderStatusWaitForMake
        - WithdrawOrderStatusWaitForDue
      - description: 用户id
        in: query
        name: user_id
        type: string
      - description: '提货方式：1：手动提货: 2：强制发货。'
        enum:
        - 1
        - 2
        format: int32
        in: query
        name: withdraw_type
        type: integer
        x-enum-varnames:
        - WithdrawTypeManual
        - WithdrawTypeForced
      - description: 云仓id
        in: query
        name: yc_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 导出发货管理列表
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/mobile_phone:
    get:
      description: 查看手机号
      parameters:
      - description: 云仓ID
        in: query
        name: yc_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetShipManageMobilePhoneResp'
              type: object
      security:
      - Bearer: []
      summary: 查看手机号
      tags:
      - 管理端-发货管理
  /admin/v1/ship_manage/orderLogs/{id}:
    get:
      description: 获取订单操作日志
      parameters:
      - description: 云仓ID
        in: query
        name: yc_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetShipManageOrderLogsResp'
              type: object
      security:
      - Bearer: []
      summary: 获取订单操作日志
      tags:
      - 管理端-发货管理
  /admin/v1/story/add:
    post:
      description: 新增故事玩法
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddStoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddStoryResp'
              type: object
      security:
      - Bearer: []
      summary: 新增故事玩法
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/del:
    post:
      description: 故事玩法删除
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelStoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelStoryResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法删除
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/detail:
    get:
      description: 查询故事玩法管理详情
      parameters:
      - description: 故事玩法ID
        in: query
        name: id
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法管理详情
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/edit:
    post:
      description: 编辑故事玩法
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditStoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditStoryResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑故事玩法
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/edit_status:
    post:
      description: 故事玩法状态编辑
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditStoryStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditStoryStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法状态编辑
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/item_story/list:
    get:
      description: 查询故事玩法的商品列表
      parameters:
      - description: 物品ID
        in: query
        name: item_id
        type: string
      - description: 物品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetItemStoryListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法的商品列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/list:
    get:
      description: 查询故事玩法管理列表
      parameters:
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 创建人
        in: query
        name: created_by
        type: string
      - description: 故事玩法ID
        in: query
        name: id
        type: integer
      - description: 故事玩法物品id(商品/组合商品)
        in: query
        name: item_id
        type: string
      - description: 故事玩法物品名称(商品/组合商品)
        in: query
        name: item_title
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 故事玩法场景id
        in: query
        name: scene_id
        type: integer
      - description: 开始时间结束
        in: query
        name: start_time_end
        type: string
      - description: 开始时间开始
        in: query
        name: start_time_start
        type: string
      - description: 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        in: query
        name: status
        type: integer
      - description: 活动名称
        in: query
        name: title
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法管理列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/log_list:
    get:
      description: 查询故事玩法日志列表
      parameters:
      - description: 故事玩法ID
        in: query
        name: story_id
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetStoryLogListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法日志列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/order/detail:
    get:
      description: 查询故事玩法订单详情
      parameters:
      - description: 订单ID
        example: "0"
        in: query
        name: id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryAdminOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法订单详情
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/order/export:
    get:
      description: 导出故事玩法订单列表，下载文件
      parameters:
      - description: 活动ID
        in: query
        name: activity_id
        type: integer
      - description: 活动名称
        in: query
        name: activity_title
        type: string
      - description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        in: query
        name: chain_status
        type: integer
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 故事玩法物品id(商品/组合商品)
        in: query
        name: item_id
        type: string
      - description: 故事玩法物品名称(商品/组合商品)
        in: query
        name: item_title
        type: string
      - description: 订单ID
        in: query
        name: order_id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 故事玩法场景id
        in: query
        name: scene_id
        type: integer
      - description: 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        in: query
        name: status
        type: integer
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryAdminOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 导出故事玩法订单列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/order/list:
    get:
      description: 查询故事玩法订单列表
      parameters:
      - description: 活动ID
        in: query
        name: activity_id
        type: integer
      - description: 活动名称
        in: query
        name: activity_title
        type: string
      - description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        in: query
        name: chain_status
        type: integer
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 故事玩法物品id(商品/组合商品)
        in: query
        name: item_id
        type: string
      - description: 故事玩法物品名称(商品/组合商品)
        in: query
        name: item_title
        type: string
      - description: 订单ID
        in: query
        name: order_id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 故事玩法场景id
        in: query
        name: scene_id
        type: integer
      - description: 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
        in: query
        name: status
        type: integer
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryAdminOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法订单列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/order_detail/list:
    get:
      description: 获取故事玩法订单详情列表
      parameters:
      - description: 订单ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryOrderDetailListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取故事玩法订单详情列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story/rule:
    get:
      description: 查询故事玩法规则
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryRuleAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法规则
      tags:
      - 管理端-故事玩法管理
    post:
      description: 编辑故事玩法规则
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditStoryRuleReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditStoryRuleResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑故事玩法规则
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story_scene/add:
    post:
      description: 新增故事玩法场景
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddStorySceneReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddStorySceneResp'
              type: object
      security:
      - Bearer: []
      summary: 新增故事玩法场景
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story_scene/detail:
    get:
      description: 查询故事玩法场景管理详情
      parameters:
      - description: 场景ID
        in: query
        name: id
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStorySceneAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法场景管理详情
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story_scene/edit:
    post:
      description: 编辑故事玩法场景
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditStorySceneReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditStorySceneResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑故事玩法场景
      tags:
      - 管理端-故事玩法管理
  /admin/v1/story_scene/list:
    get:
      description: 查询故事玩法场景管理列表
      parameters:
      - description: 是否需要活动数量 0:不需要 1:需要
        in: query
        name: need_story_num
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStorySceneAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法场景管理列表
      tags:
      - 管理端-故事玩法管理
  /admin/v1/synthesis/add:
    post:
      description: 新增合成活动
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddSynthesisReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddSynthesisResp'
              type: object
      security:
      - Bearer: []
      summary: 新增合成活动
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/del:
    post:
      description: 合成活动删除
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelSynthesisReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelSynthesisResp'
              type: object
      security:
      - Bearer: []
      summary: 合成活动删除
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/detail:
    get:
      description: 查询合成管理详情
      parameters:
      - description: 合成活动ID
        in: query
        name: id
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisAdminDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成管理详情
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/edit:
    post:
      description: 编辑合成活动
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditSynthesisReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditSynthesisResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑合成活动
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/edit_status:
    post:
      description: 合成活动状态编辑
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditSynthesisStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditSynthesisStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 合成活动状态编辑
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/list:
    get:
      description: 查询合成管理列表
      parameters:
      - description: 合成类型【1-优先购权益；2-商品】
        in: query
        name: activity_type
        type: integer
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 创建人
        in: query
        name: created_by
        type: string
      - description: 合成活动ID
        in: query
        name: id
        type: integer
      - description: 合成物品id(商品/优先购)
        in: query
        name: item_id
        type: string
      - description: 合成物品名称(商品/优先购)
        in: query
        name: item_title
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 开始时间结束
        in: query
        name: start_time_end
        type: string
      - description: 开始时间开始
        in: query
        name: start_time_start
        type: string
      - description: 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
        in: query
        name: status
        type: integer
      - description: 活动名称
        in: query
        name: title
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisAdminListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成管理列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/log_list:
    get:
      description: 查询合成活动日志列表
      parameters:
      - description: 合成活动ID
        in: query
        name: synthesis_id
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetSynthesisLogListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询合成活动日志列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/order/detail:
    get:
      description: 查询合成订单详情
      parameters:
      - description: 订单ID
        example: "0"
        in: query
        name: id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisAdminOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成订单详情
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/order/export:
    get:
      description: 导出合成订单列表，下载文件
      parameters:
      - description: 活动ID
        in: query
        name: activity_id
        type: integer
      - description: 活动名称
        in: query
        name: activity_title
        type: string
      - description: 合成类型【1-优先购权益；2-商品】
        in: query
        name: activity_type
        type: integer
      - description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        in: query
        name: chain_status
        type: integer
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 合成物品id(商品/优先购)
        in: query
        name: item_id
        type: string
      - description: 合成物品名称(商品/优先购)
        in: query
        name: item_title
        type: string
      - description: 订单ID
        in: query
        name: order_id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 订单状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
        in: query
        name: status
        type: integer
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisAdminOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 导出合成订单列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/order/list:
    get:
      description: 查询合成订单列表
      parameters:
      - description: 活动ID
        in: query
        name: activity_id
        type: integer
      - description: 活动名称
        in: query
        name: activity_title
        type: string
      - description: 合成类型【1-优先购权益；2-商品】
        in: query
        name: activity_type
        type: integer
      - description: 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
        in: query
        name: chain_status
        type: integer
      - description: 创建时间结束
        in: query
        name: created_at_end
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_start
        type: string
      - description: 合成物品id(商品/优先购)
        in: query
        name: item_id
        type: string
      - description: 合成物品名称(商品/优先购)
        in: query
        name: item_title
        type: string
      - description: 订单ID
        in: query
        name: order_id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 订单状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
        in: query
        name: status
        type: integer
      - description: 用户ID
        in: query
        name: user_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisAdminOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成订单列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/order_detail/list:
    get:
      description: 获取合成订单详情列表
      parameters:
      - description: 订单ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisOrderDetailListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取合成订单详情列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/priority_buy/list:
    get:
      description: 查询优先购列表
      parameters:
      - description: 创建人
        in: query
        name: creator
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 优先购ID
        in: query
        name: priority_buy_id
        type: string
      - description: 优先购名称
        in: query
        name: priority_buy_name
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetPriorityBuyListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询优先购列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/prize_item/list:
    get:
      description: 查询融合商品奖品列表
      parameters:
      - description: 物品ID
        in: query
        name: item_id
        type: string
      - description: 物品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetItemSynthesisListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询融合商品奖品列表
      tags:
      - 管理端-合成活动管理
  /admin/v1/synthesis/rule:
    get:
      description: 查询合成规则
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisRuleAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成规则
      tags:
      - 管理端-合成活动管理
    post:
      description: 编辑合成规则
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditSynthesisRuleReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditSynthesisRuleResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑合成规则
      tags:
      - 管理端-合成活动管理
  /admin/v1/user/user_bind/manual_bind:
    post:
      description: 人工绑定邀请
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.BindReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.BindResp'
              type: object
      security:
      - Bearer: []
      summary: 人工绑定邀请
      tags:
      - 管理端
  /open/v1/announcement/schedule_publish:
    post:
      description: 定时发布公告
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/app_service_apps_business_announcement_define.SchedulePublishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/app_service_apps_business_announcement_define.SchedulePublishResp'
              type: object
      security:
      - Bearer: []
      summary: 定时发布公告
      tags:
      - Open-公告管理
  /open/v1/asset/user_wallet/do_user_bonus_expire:
    post:
      description: 处理金币过期
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.BonusExpireResp'
              type: object
      security:
      - Bearer: []
      summary: 处理金币过期
      tags:
      - Open端
  /open/v1/bonus_mall/bonus_item/finish:
    post:
      description: 自动结束到期的积分商品活动
      parameters:
      - description: 参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.FinishBonusItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.FinishBonusItemResp'
              type: object
      security:
      - Bearer: []
      summary: 自动结束到期的积分商品活动
      tags:
      - Open端-积分商城
  /open/v1/bonus_mall/exchange_log/check_and_alarm:
    post:
      description: 检查并告警积分兑换异常情况
      parameters:
      - description: 参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CheckAndAlarmAbnormalExchangeLogReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CheckAndAlarmAbnormalExchangeLogResp'
              type: object
      security:
      - Bearer: []
      summary: 检查并告警积分兑换异常情况
      tags:
      - Open端-积分商城
  /open/v1/bonus_mall/exchange_log/clear_expired_cache:
    post:
      description: 清除过期的积分兑换限制缓存
      parameters:
      - description: 参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ClearExpiredExchangeLimitCacheReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ClearExpiredExchangeLimitCacheResp'
              type: object
      security:
      - Bearer: []
      summary: 清除过期的积分兑换限制缓存
      tags:
      - Open端-积分商城
  /open/v1/market_changes/schedule_publish:
    post:
      description: 定时发布行情异动
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/app_service_apps_business_market_changes_define.SchedulePublishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/app_service_apps_business_market_changes_define.SchedulePublishResp'
              type: object
      security:
      - Bearer: []
      summary: 定时发布行情异动
      tags:
      - Open-行情异动管理
  /open/v1/notice/aggregate:
    get:
      description: 聚合公告和活动
      parameters:
      - description: 广告商ID
        in: query
        name: ad_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/app_service_apps_business_announcement_define.NoticeAggregateResp'
              type: object
      security:
      - Bearer: []
      summary: 聚合公告和活动
      tags:
      - Open-公告管理
  /open/v1/operation_announcement/schedule_publish:
    post:
      description: 定时发布运营公告
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/app_service_apps_business_operation_announcement_define.SchedulePublishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/app_service_apps_business_operation_announcement_define.SchedulePublishResp'
              type: object
      security:
      - Bearer: []
      summary: 定时发布运营公告
      tags:
      - Open-运营方公告管理
  /open/v1/story/finish:
    post:
      description: 故事玩法超时下架
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryFinishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryFinishResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法超时下架
      tags:
      - open端-故事玩法管理
  /open/v1/story/item_story_is_up:
    get:
      description: 获取商品对应故事活动是否上架
      parameters:
      - description: 物品id
        in: query
        name: item_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetItemStoryIsUpResp'
              type: object
      security:
      - Bearer: []
      summary: 获取商品对应故事活动是否上架
      tags:
      - open端-故事玩法管理
  /open/v1/story/materials/get_release_time:
    post:
      description: 获取故事玩法材料释放时间
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryMaterialsGetReleaseTimeReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryMaterialsGetReleaseTimeResp'
              type: object
      security:
      - Bearer: []
      summary: 获取故事玩法材料释放时间
      tags:
      - open端-故事玩法管理
  /open/v1/story/materials/release:
    post:
      description: 故事玩法材料释放
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryMaterialsReleaseReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryMaterialsReleaseResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法材料释放
      tags:
      - open端-故事玩法管理
  /open/v1/story/order/complete:
    post:
      description: 故事玩法探索完成
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryOrderCompleteReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryOrderCompleteResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法探索完成
      tags:
      - open端-故事玩法管理
  /open/v1/story/order/up_chain:
    post:
      description: 故事玩法订单上链
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryOrderUpChainReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryOrderUpChainResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法订单上链
      tags:
      - open端-故事玩法管理
  /open/v1/synthesis/finish:
    post:
      description: 合成活动超时下架
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SynthesisFinishReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SynthesisFinishResp'
              type: object
      security:
      - Bearer: []
      summary: 合成活动超时下架
      tags:
      - open端-合成活动管理
  /open/v1/synthesis/item_is_up:
    get:
      description: 获取合成活动是否上架
      parameters:
      - description: 物品id
        in: query
        name: item_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetItemIsUpResp'
              type: object
      security:
      - Bearer: []
      summary: 获取合成活动是否上架
      tags:
      - open端-合成活动管理
  /open/v1/synthesis/materials/get_release_time:
    post:
      description: 获取合成材料释放时间
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SynthesisMaterialsGetReleaseTimeReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SynthesisMaterialsGetReleaseTimeResp'
              type: object
      security:
      - Bearer: []
      summary: 获取合成材料释放时间
      tags:
      - open端-合成活动管理
  /open/v1/synthesis/materials/release:
    post:
      description: 合成材料释放
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SynthesisMaterialsReleaseReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SynthesisMaterialsReleaseResp'
              type: object
      security:
      - Bearer: []
      summary: 合成材料释放
      tags:
      - open端-合成活动管理
  /open/v1/synthesis/order/up_chain:
    post:
      description: 合成订单上链
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SynthesisOrderUpChainReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SynthesisOrderUpChainResp'
              type: object
      security:
      - Bearer: []
      summary: 合成订单上链
      tags:
      - open端-合成活动管理
  /open/v1/synthesis/priority_buy_is_up:
    get:
      description: 获取优先购合成活动是否上架
      parameters:
      - description: 优先购id
        in: query
        name: priority_buy_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetPriorityBuyIsUpResp'
              type: object
      security:
      - Bearer: []
      summary: 获取优先购合成活动是否上架
      tags:
      - open端-合成活动管理
  /open/v1/trade/circulation_item/batch_update:
    post:
      description: 批量更新二手商品
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.BatchUpdateCirculationItemResp'
              type: object
      security:
      - Bearer: []
      summary: 批量更新二手商品
      tags:
      - open端-交易所
  /open/v1/trade/circulation_item/sync:
    post:
      description: 同步二手商品数据
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SyncCirculationItemResp'
              type: object
      security:
      - Bearer: []
      summary: 同步二手商品数据
      tags:
      - open端-交易所
  /open/v1/trade/market/update_overview:
    post:
      description: 更新概览数据
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UpdateMarketOverviewResp'
              type: object
      security:
      - Bearer: []
      summary: 更新概览数据
      tags:
      - open端-交易所
  /web/v1/announcement/category/list:
    get:
      description: 查询公告分类列表（Web端）
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetAnnCategoryWebListResp'
      summary: 查询公告分类列表
      tags:
      - 用户端-平台方公告分类
  /web/v1/announcement/detail:
    get:
      description: 查询公告详情（Web端）
      parameters:
      - description: 公告ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetAnnouncementWebDetailResp'
      summary: 查询公告详情
      tags:
      - 用户端-平台方公告
  /web/v1/announcement/list:
    get:
      description: 查询公告列表（Web端）
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 关键字(搜索标题和内容，限输20个字符)
        in: query
        maxLength: 20
        name: keyword
        type: string
      - description: 排序字段，支持：publish_time
        in: query
        name: order_by
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: '排序方式，desc: 倒序，asc: 升序'
        in: query
        name: sort_order
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetAnnouncementWebListResp'
      summary: 查询公告列表
      tags:
      - 用户端-平台方公告
  /web/v1/asset/user_wallet/receive_bonus:
    post:
      description: 领取奖励
      parameters:
      - description: 查询参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ReceiveBonusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ReceiveBonusResp'
              type: object
      security:
      - Bearer: []
      summary: 领取奖励
      tags:
      - 用户端
  /web/v1/asset/user_wallet/user_bonus_log_list:
    get:
      description: 获取用户金币流水日志列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 1-未解锁 10-待领取 20-已领取 30-已使用 99-过期未领取
        in: query
        name: receive_status_list
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetUserBonusLogResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户金币流水日志列表
      tags:
      - 用户端
  /web/v1/bonus_mall/bonus_item/detail:
    get:
      description: 获取积分商品详情
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetBonusItemWebDetailReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetBonusItemWebDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取积分商品详情
      tags:
      - 用户端-积分商城
  /web/v1/bonus_mall/bonus_item/exchange:
    post:
      description: 兑换积分商品
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UserExchangeBonusItemReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UserExchangeBonusItemResp'
              type: object
      security:
      - Bearer: []
      summary: 兑换积分商品
      tags:
      - 用户端-积分商城
  /web/v1/bonus_mall/bonus_item/list:
    get:
      description: 获取积分商品列表
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetBonusItemWebListReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetBonusItemWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取积分商品列表
      tags:
      - 用户端-积分商城
  /web/v1/bonus_mall/exchange_log/list:
    get:
      description: 获取用户兑换记录列表
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetUserExchangeLogWebListReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetUserExchangeLogWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户兑换记录列表
      tags:
      - 用户端-积分商城
  /web/v1/bonus_mall/exchange_log/top_list:
    get:
      description: 获取最新兑换记录列表
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetExchangeLogWebTopListReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetExchangeLogWebTopListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取最新兑换记录列表
      tags:
      - 用户端-积分商城
  /web/v1/common/category/list:
    get:
      description: 分类列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 关联类型
        enum:
        - 1
        format: int32
        in: query
        name: relate_type
        required: true
        type: integer
        x-enum-comments:
          CategoryRelateTypeMarketChanges: 行情异动
        x-enum-descriptions:
        - 行情异动
        x-enum-varnames:
        - CategoryRelateTypeMarketChanges
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCategoryWebListResp'
              type: object
      summary: 分类列表
      tags:
      - 用户端-分类
  /web/v1/common/generate_id:
    get:
      description: 生成Snowflake唯一ID
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GenerateIDResp'
              type: object
      summary: 生成唯一ID
      tags:
      - 用户端-通用工具
      x-apifox-folder: 用户端/通用工具
  /web/v1/conversations/add:
    post:
      description: 创建或获取与指定用户的会话，基于双向记录设计，会自动创建双方的会话记录
      parameters:
      - description: 创建会话参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CreateConversationReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.CreateConversationResp'
      summary: 创建会话
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/会话管理
  /web/v1/conversations/detail:
    get:
      description: 获取会话详情信息
      parameters:
      - description: 会话记录ID
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetConversationDetailResp'
      summary: 获取会话详情
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/会话管理
  /web/v1/conversations/list:
    get:
      description: 获取当前用户的会话列表，基于双向记录设计，每个用户只能看到自己的会话记录。返回数据中包含对方的信息（other_participant_*字段），客户端直接使用这些字段显示对方信息即可
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetConversationListResp'
      summary: 获取会话列表
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/会话管理
  /web/v1/conversations/messages/add:
    post:
      description: 在指定会话中发送消息（文本、图片或帖子快照）
      parameters:
      - description: 发送消息参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SendMessageReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.SendMessageResp'
      summary: 发送消息
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/消息管理
  /web/v1/conversations/messages/list:
    get:
      description: 获取指定会话的消息列表
      parameters:
      - description: 会话ID
        in: query
        name: conversation_id
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetMessageListResp'
      summary: 获取消息列表
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/消息管理
  /web/v1/invite_reward/invite_list:
    get:
      description: 获取用户奖励列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetUserInviteListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户奖励列表
      tags:
      - 用户端
  /web/v1/invite_reward/invite_summary:
    get:
      description: 获取用户邀请总览
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetInviteSummaryResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户邀请总览
      tags:
      - 用户端
  /web/v1/market_changes/detail:
    get:
      description: 行情异动详情
      parameters:
      - description: 行情异动ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketChangesWebDetailResp'
              type: object
      summary: 行情异动详情
      tags:
      - 用户端-行情异动
  /web/v1/market_changes/latest_detail:
    get:
      description: 根据商品获取最新一条已发布的行情异动详情
      parameters:
      - description: 商品ID
        in: query
        name: item_id
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketChangesWebDetailResp'
              type: object
      summary: 根据商品获取最新一条行情异动详情
      tags:
      - 用户端-行情异动
  /web/v1/market_changes/list:
    get:
      description: 行情异动列表
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 关键字(搜索标题,内容和商品名称，限输20个字符)
        in: query
        maxLength: 20
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketChangesWebListResp'
              type: object
      summary: 行情异动列表
      tags:
      - 用户端-行情异动
  /web/v1/merchant_application/add:
    post:
      description: 用户提交成为商家的申请
      parameters:
      - description: 申请参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SubmitMerchantApplicationReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.SubmitMerchantApplicationResp'
      summary: 提交商家申请
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/商家申请
  /web/v1/merchant_application/status:
    get:
      description: 获取当前用户的商家申请状态
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetMerchantApplicationStatusResp'
      summary: 获取商家申请状态
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/商家申请
  /web/v1/notification/device/register:
    post:
      description: 注册设备
      parameters:
      - description: 注册参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.RegisterDeviceReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.RegisterDeviceResp'
              type: object
      security:
      - Bearer: []
      summary: 注册设备
      tags:
      - 用户端-消息通知
  /web/v1/notification/device/update_status:
    post:
      description: 更新设备状态
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdateDeviceStatusReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UpdateDeviceStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 更新设备状态
      tags:
      - 用户端-消息通知
  /web/v1/notification/push/test:
    post:
      description: 测试推送（仅测试环境可用）
      parameters:
      - description: 推送参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TestPushReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TestPushResp'
              type: object
      security:
      - Bearer: []
      summary: 测试推送（仅测试环境可用）
      tags:
      - 用户端-消息通知
  /web/v1/operation_announcement/category/list:
    get:
      description: 查询运营公告分类列表（Web端）
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetOperationAnnCategoryWebListResp'
      summary: 查询运营公告分类列表
      tags:
      - 用户端-运营方公告分类
  /web/v1/operation_announcement/detail:
    get:
      description: 查询运营公告详情（Web端）
      parameters:
      - description: 运营公告ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetOperationAnnouncementWebDetailResp'
      summary: 查询运营公告详情
      tags:
      - 用户端-运营方公告
  /web/v1/operation_announcement/list:
    get:
      description: 查询运营公告列表（Web端）
      parameters:
      - description: 分类ID
        example: "0"
        in: query
        name: category_id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 关键字(搜索标题和内容，限输20个字符)
        in: query
        maxLength: 20
        name: keyword
        type: string
      - description: 排序字段，支持：publish_time
        in: query
        name: order_by
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: '排序方式，desc: 倒序，asc: 升序'
        in: query
        name: sort_order
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetOperationAnnouncementWebListResp'
      summary: 查询运营公告列表
      tags:
      - 用户端-运营方公告
  /web/v1/posts/add:
    post:
      description: 商家发布求购帖子
      parameters:
      - description: 创建帖子参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CreatePostReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.CreatePostResp'
      summary: 创建帖子
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/delete:
    post:
      description: 商家删除自己的帖子
      parameters:
      - description: 删除参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DeletePostReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.DeletePostResp'
      summary: 删除帖子
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/detail:
    get:
      description: 获取指定帖子的详细信息
      parameters:
      - description: 帖子ID
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetPostDetailResp'
      summary: 获取帖子详情
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/edit:
    post:
      description: 商家编辑自己帖子的内容（描述、价格、媒体文件）。
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditPostReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.EditPostResp'
      summary: 编辑帖子内容
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/edit_status:
    post:
      description: 商家更新自己帖子的状态（上架/下架）
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdatePostStatusReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.UpdatePostStatusResp'
      summary: 更新帖子状态
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/list:
    get:
      description: 获取求购帖子列表，支持关键字搜索和价格筛选
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetPostListResp'
      summary: 获取帖子列表
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/posts/my:
    get:
      description: 获取当前商家发布的帖子列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
        enum:
        - -3
        - -2
        - -1
        - 1
        in: query
        name: status
        type: integer
        x-enum-comments:
          PostStatusActive: 已上架
          PostStatusDeleted: 已删除
          PostStatusInactive: 已下架
          PostStatusViolation: 违规下架
        x-enum-descriptions:
        - 违规下架
        - 已下架
        - 已删除
        - 已上架
        x-enum-varnames:
        - PostStatusViolation
        - PostStatusInactive
        - PostStatusDeleted
        - PostStatusActive
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetMyPostListResp'
      summary: 获取我的帖子
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/帖子管理
  /web/v1/smart_reply_template/detail:
    get:
      description: 获取商家的智能回复模板和开关状态
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.GetSmartReplyTemplateResp'
      summary: 获取智能回复模板
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/智能回复
  /web/v1/smart_reply_template/edit:
    post:
      description: 更新商家的智能回复模板内容
      parameters:
      - description: 更新模板参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdateSmartReplyTemplateReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.UpdateSmartReplyTemplateResp'
      summary: 更新智能回复模板
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/智能回复
  /web/v1/smart_reply_template/toggle:
    post:
      description: 开启或关闭商家的智能回复功能
      parameters:
      - description: 切换开关参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ToggleSmartReplyReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/define.ToggleSmartReplyResp'
      summary: 切换智能回复开关
      tags:
      - 用户端-卡牌集社
      x-apifox-folder: 用户端/智能回复
  /web/v1/story/detail:
    get:
      description: 查询故事玩法详情
      parameters:
      - description: 活动编码
        in: query
        name: activity_code
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryWebDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法详情
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/discovery:
    post:
      description: 发起故事玩法探索
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.StoryDiscoveryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.StoryDiscoveryResp'
              type: object
      security:
      - Bearer: []
      summary: 发起故事玩法探索
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/list:
    get:
      description: 查询故事玩法列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 故事玩法场景id
        in: query
        name: scene_id
        required: true
        type: integer
      - description: 故事玩法状态 4:已结束
        in: query
        name: status
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法列表
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/order/detail:
    get:
      description: 查询故事玩法订单详情
      parameters:
      - description: 故事玩法订单编号
        in: query
        name: order_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryWebOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法订单详情
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/order/list:
    get:
      description: 查询故事玩法订单列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 故事玩法场景id
        in: query
        name: scene_id
        type: integer
      - description: 订单状态【21:探索中(云仓材料消耗成功);91:已完成】
        in: query
        name: status
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryWebOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法订单列表
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/order/status:
    get:
      description: 获取故事玩法订单状态
      parameters:
      - description: 故事玩法订单编号
        in: query
        name: order_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryWebOrderStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 获取故事玩法订单状态
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/receive:
    post:
      description: 故事玩法领取奖励
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ReceiveStoryReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ReceiveStoryResp'
              type: object
      security:
      - Bearer: []
      summary: 故事玩法领取奖励
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/rule:
    get:
      description: 查询故事玩法规则
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryRuleAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法规则
      tags:
      - 用户端-故事玩法管理
  /web/v1/story/user/materials_list:
    get:
      description: 获取我的故事玩法探索材料列表
      parameters:
      - description: 成本价排序顺序 1:升序 -1:降序
        in: query
        name: cost_price_order
        type: integer
      - description: 物品id
        in: query
        name: item_id
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStoryUserMaterialsListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取我的故事玩法探索材料列表
      tags:
      - 用户端-故事玩法管理
  /web/v1/story_scene/home:
    get:
      description: 查询故事玩法场景首页列表
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStorySceneWebHomeResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法场景首页列表
      tags:
      - 用户端-故事玩法管理
  /web/v1/story_scene/list:
    get:
      description: 查询故事玩法场景列表
      parameters:
      - description: 是否需要活动数量 0:不需要 1:需要
        in: query
        name: need_story_num
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetStorySceneWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询故事玩法场景列表
      tags:
      - 用户端-故事玩法管理
  /web/v1/synthesis/detail:
    get:
      description: 查询合成活动详情
      parameters:
      - description: 活动编码
        in: query
        name: activity_code
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisWebDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成活动详情
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/launch:
    post:
      description: 发起合成
      parameters:
      - description: 新增参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.LaunchSynthesisReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.LaunchSynthesisResp'
              type: object
      security:
      - Bearer: []
      summary: 发起合成
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/list:
    get:
      description: 查询合成活动列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成活动列表
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/order/detail:
    get:
      description: 查询合成活动订单详情
      parameters:
      - description: 合成订单编号
        in: query
        name: order_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisWebOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成活动订单详情
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/order/list:
    get:
      description: 查询合成活动订单列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisWebOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成活动订单列表
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/order/status:
    get:
      description: 获取合成订单状态
      parameters:
      - description: 合成订单编号
        in: query
        name: order_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisWebOrderStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 获取合成订单状态
      tags:
      - 用户端-合成活动管理
  /web/v1/synthesis/rule:
    get:
      description: 查询合成规则
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetSynthesisRuleAdminResp'
              type: object
      security:
      - Bearer: []
      summary: 查询合成规则
      tags:
      - 用户端-合成活动管理
  /web/v1/system/get_js_sdk:
    get:
      description: 获取公众号SDK
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetJsSDKReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetJsSDKResp'
              type: object
      security:
      - Bearer: []
      summary: 获取公众号SDK
      tags:
      - 用户端
  /web/v1/trade/activities:
    get:
      description: 查询动态（公告、行情异动等）
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebActivitiesResp'
              type: object
      security:
      - Bearer: []
      summary: 查询动态（公告、行情异动等）
      tags:
      - 用户端-交易所
  /web/v1/trade/circulation_item/list:
    get:
      description: 查询二手列表（含榜单）
      parameters:
      - description: 排序字段，transaction_amount(成交额) | market_amount(市值) | price_change_rate(涨跌幅)
        in: query
        name: order_by
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 排序方式，asc(升序) | desc(降序)
        in: query
        name: sort_order
        type: string
      - description: '类型，all: 全部（二手默认列表页），hot: 热榜，transaction_amount: 成交额榜，market_amount:
          市值榜'
        enum:
        - all
        - hot
        - transaction_amount
        - market_amount
        in: query
        name: type
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCirculationItemWebListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询二手列表（含榜单）
      tags:
      - 用户端-交易所
  /web/v1/trade/circulation_item/overview:
    get:
      description: 查询榜单总览数据
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCirculationItemWebOverviewResp'
              type: object
      security:
      - Bearer: []
      summary: 查询榜单总览数据
      tags:
      - 用户端-交易所
  /web/v1/trade/circulation_item/top_list:
    get:
      description: 查询二手入口商品列表
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetCirculationItemWebTopListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询二手入口商品列表
      tags:
      - 用户端-交易所
  /web/v1/trade/issue_item/top_list:
    get:
      description: 查询一手入口商品列表
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetIssueItemWebTopListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询一手入口商品列表
      tags:
      - 用户端-交易所
  /web/v1/trade/market/overview:
    get:
      description: 查询市场总览数据
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetMarketWebOverviewResp'
              type: object
      security:
      - Bearer: []
      summary: 查询市场总览数据
      tags:
      - 用户端-交易所
  /web/v1/user/user/get_user_info:
    get:
      description: 获取用户信息
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.GetUserInfoReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetUserInfoResp'
              type: object
      security:
      - Bearer: []
      summary: 获取用户信息
      tags:
      - 用户端
      x-apifox-folder: 用户端/用户信息
  /web/v1/user/user_bind/bind:
    post:
      description: 绑定邀请码
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.BindReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.BindResp'
              type: object
      security:
      - Bearer: []
      summary: 绑定邀请码
      tags:
      - 用户端
  /web/v1/yc/all_items:
    get:
      description: 查询云仓商品列表
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebAllItemsResp'
              type: object
      security:
      - Bearer: []
      summary: 查询云仓商品列表
      tags:
      - 用户端-云仓商品管理
  /web/v1/yc/items/{item_id}/user_item_list:
    get:
      description: 查询云仓每个商品的持仓列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - in: query
        name: sort
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebUserItemListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询云仓每个商品的持仓列表
      tags:
      - 用户端-云仓商品管理
  /web/v1/yc/statistic_overview:
    get:
      description: 查询云仓数据面板
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebStatisticOverviewResp'
              type: object
      security:
      - Bearer: []
      summary: 查询云仓数据面板
      tags:
      - 用户端-云仓商品管理
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
