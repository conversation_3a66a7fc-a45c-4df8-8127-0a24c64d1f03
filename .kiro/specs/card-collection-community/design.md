# 设计文档

## 概述

卡牌集社是一个基于帖子发布和IM聊天的卡牌交易平台。商家发布求购帖子，收藏用户查看帖子并与商家沟通。系统采用传统的HTTP请求响应模式，通过记录最后阅读时间来管理未读消息状态。

系统包含两个主要端：
- **用户端**: 商家发布求购帖子，用户浏览帖子并与商家沟通
- **管理端**: 管理员管理求购帖子、会话记录和商家审核

## 架构

### 业务流程UML

#### 1. 帖子发布流程时序图

```mermaid
sequenceDiagram
    participant M as 商家
    participant API as API服务
    participant DB as 数据库
    participant COS as 文件存储

    M->>API: POST /web/v1/posts
    API->>API: 验证用户身份
    API->>API: 验证帖子数据
    
    alt 包含图片
        API->>COS: 上传图片
        COS-->>API: 返回图片URL
    end
    
    API->>DB: 保存帖子数据
    DB-->>API: 返回帖子ID
    API-->>M: 返回创建成功
    
    M->>API: GET /web/v1/posts/my
    API->>DB: 查询商家帖子列表
    DB-->>API: 返回帖子列表
    API-->>M: 显示我的发布
```

#### 2. 用户联系商家流程时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant DB as 数据库


    U->>API: GET /web/v1/posts
    API->>DB: 查询帖子列表
    DB-->>API: 返回帖子数据
    API-->>U: 显示帖子列表
    
    U->>API: GET /web/v1/posts/{id}
    API->>DB: 查询帖子详情
    DB-->>API: 返回帖子详情
    API-->>U: 显示帖子详情
    
    U->>API: POST /web/v1/conversations
    API->>API: 验证用户身份
    API->>DB: 检查用户与商家的会话是否存在
    
    alt 会话不存在
        API->>DB: 创建新会话(user_id + merchant_id)
        DB-->>API: 返回会话ID
        
        API->>DB: 检查商家智能回复设置
        alt 智能回复开启
            API->>DB: 发送智能回复消息
            API->>DB: 更新会话最后消息信息
        end
    else 会话已存在
        API->>DB: 获取现有会话信息
        DB-->>API: 返回会话数据
    end
    
    API-->>U: 返回会话信息
    U->>API: 跳转到聊天页面
```

#### 3. 消息收发流程时序图（引入消息队列）

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant DB as MySQL数据库
    participant MQ as Kafka消息队列
    participant Consumer as 消息消费者
    participant Cache as Redis缓存
    participant Push as 推送服务
    participant M as 商家

    U->>API: GET /web/v1/conversations/{id}/messages
    API->>DB: 查询消息列表(单表分页查询)
    DB-->>API: 返回消息数据
    API->>DB: 更新用户最后阅读时间
    API->>MQ: 发送缓存更新消息
    API-->>U: 显示消息列表
    
    U->>API: POST /web/v1/conversations/{id}/messages
    API->>API: 验证消息内容和client_msg_id
    
    alt 图片消息
        API->>COS: 上传图片
        COS-->>API: 返回图片URL
    end
    
    Note over API,DB: 同步处理核心数据
    API->>API: 开启数据库事务
    API->>DB: 保存消息到messages表(利用uk_client_msg_id防重复)
    DB-->>API: 返回消息ID
    API->>DB: 更新conversations表最后消息信息
    DB-->>API: 更新成功
    API->>API: 提交事务
    API-->>U: 返回发送成功
    
    Note over API,MQ: 异步处理扩展功能
    API->>MQ: 发送消息事件(message.sent)
    API->>MQ: 发送智能回复触发事件(smart.reply.trigger)
    API->>MQ: 发送推送通知事件(notification.push)
    API->>MQ: 发送缓存更新事件(cache.update)
    
    Note over Consumer: 异步消费处理
    MQ->>Consumer: 消费智能回复事件
    Consumer->>DB: 检查智能回复设置
    Consumer->>DB: 发送智能回复消息
    
    MQ->>Consumer: 消费推送通知事件
    Consumer->>Push: 发送推送通知给商家
    
    MQ->>Consumer: 消费缓存更新事件
    Consumer->>Cache: 更新未读计数缓存
    Consumer->>Cache: 清除相关缓存
    
    Note over M: 商家查看消息
    M->>API: GET /web/v1/conversations
    API->>Cache: 从缓存获取会话列表
    Cache-->>API: 返回缓存数据
    API-->>M: 显示会话列表(含未读数)
```

#### 4. 智能回复管理流程活动图

```mermaid
flowchart TD
    A[商家进入我的发布页面] --> B[查询智能回复模板和状态]
    B --> C{是否已设置模板?}
    
    C -->|否| D[显示未设置提示]
    C -->|是| E{智能回复开关状态?}
    
    E -->|开启| F[显示智能回复开启状态]
    E -->|关闭| G[显示智能回复关闭状态]
    
    D --> H[点击设置智能回复]
    F --> I[点击关闭开关]
    G --> J[点击开启开关]
    
    H --> K[进入模板编辑页面]
    I --> L[更新状态为关闭]
    J --> M[更新状态为开启]
    
    K --> N[输入模板内容]
    N --> O[验证模板内容]
    O --> P{内容是否有效?}
    
    P -->|否| Q[显示验证错误]
    P -->|是| R[保存模板到数据库]
    
    Q --> N
    R --> S[设置智能回复为开启]
    
    L --> T[更新数据库状态]
    M --> T
    S --> T
    
    T --> U[返回页面并刷新显示]
    U --> V[操作完成]
```

#### 5. 帖子状态管理流程活动图

```mermaid
flowchart TD
    A[商家查看我的发布] --> B[显示帖子列表]
    B --> C{选择状态筛选}
    
    C -->|已上架| D[显示已上架帖子]
    C -->|已下架| E[显示已下架帖子]
    
    D --> F[点击帖子操作]
    E --> G[点击帖子操作]
    
    F --> H{选择操作}
    G --> I{选择操作}
    
    H -->|下架| J[更新状态为已下架]
    H -->|删除| K[更新状态为已删除]
    
    I -->|上架| L[更新状态为已上架]
    I -->|删除| M[更新状态为已删除]
    
    J --> N[更新数据库]
    K --> N
    L --> N
    M --> N
    
    N --> O[刷新帖子列表]
    O --> P{影响相关会话?}
    
    P -->|是| Q[更新会话可见性]
    P -->|否| R[完成操作]
    
    Q --> R
```

#### 6. 管理端求购管理流程活动图

```mermaid
flowchart TD
    A[管理员进入求购管理页面] --> B[设置筛选条件]
    B --> C[查询求购帖子列表]
    C --> D[显示帖子列表]
    
    D --> E{选择操作}
    E -->|查看详情| F[显示帖子详情弹窗]
    E -->|违规下架| G[显示违规确认弹窗]
    
    F --> H[查看帖子完整信息]
    H --> I[关闭详情弹窗]
    
    G --> J{确认违规下架?}
    J -->|是| K[更新帖子状态为违规下架]
    J -->|否| L[取消操作]
    
    K --> M[记录操作日志]
    M --> N[刷新帖子列表]
    
    I --> D
    L --> D
    N --> D
```

#### 7. 管理端会话管理流程活动图

```mermaid
flowchart TD
    A[管理员进入会话管理页面] --> B[设置筛选条件]
    B --> C[查询会话列表]
    C --> D[显示会话列表]
    
    D --> E{选择操作}
    E -->|查看详情| F[显示会话详情弹窗]
    
    F --> G[加载完整聊天记录]
    G --> H[显示聊天内容]
    H --> I[查看消息详情]
    I --> J[关闭详情弹窗]
    
    J --> D
```

#### 8. 消息已读反馈流程时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant ConvDB as MySQL主库
    participant Cache as Redis缓存

    Note over U,Cache: 用户进入聊天页面，自动标记为已读
    U->>API: GET /web/v1/conversations/{id}/messages
    API->>ConvDB: 查询消息列表
    ConvDB-->>API: 返回消息数据
    
    Note over API: 自动更新最后阅读时间
    API->>ConvDB: UPDATE conversations SET last_read_time = NOW()
    ConvDB-->>API: 更新成功
    
    API->>Cache: 清除未读计数缓存
    API-->>U: 返回消息列表
    
    Note over U,Cache: 用户主动标记会话为已读
    U->>API: POST /web/v1/conversations/read
    API->>API: 验证用户权限和会话存在性
    
    API->>ConvDB: 更新用户在该会话的最后阅读时间
    ConvDB-->>API: 返回更新结果
    
    API->>ConvDB: 重新计算未读消息数量
    ConvDB-->>API: 返回未读计数
    
    API->>Cache: 更新未读计数缓存
    API-->>U: 返回操作结果和未读计数
    

```

#### 9. 管理端商家审核流程活动图

```mermaid
flowchart TD
    A[管理员进入商家管理页面] --> B[查询商家申请列表]
    B --> C[显示申请列表]
    
    C --> D{选择操作}
    D -->|审核通过| E[显示通过确认弹窗]
    D -->|审核不通过| F[显示不通过确认弹窗]
    
    E --> G{确认通过?}
    F --> H{确认不通过?}
    
    G -->|是| I[更新申请状态为通过]
    G -->|否| J[取消操作]
    
    H -->|是| K[更新申请状态为不通过]
    H -->|否| L[取消操作]
    
    I --> M[更新用户类型为商家]
    K --> N[记录审核原因]
    
    M --> O[记录操作日志]
    N --> O
    
    O --> P[发送审核结果通知]
    P --> Q[刷新申请列表]
    
    J --> C
    L --> C
    Q --> C
```

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[移动端App] --> B[Web端]
    end
    
    subgraph "API网关层"
        C[Gin路由] --> D[中间件]
        D --> E[认证授权]
        D --> F[参数验证]
    end
    
    subgraph "业务服务层"
        G[帖子服务] --> H[会话服务]
        H --> I[消息服务]
        I --> J[智能回复服务]
        J --> K[管理端服务]
        L[通知服务] --> M[数据分析服务]
        N[消息队列服务]
    end
    
    subgraph "消息队列层"
        O[Kafka集群]
        P[Topic: message_events] --> O
        Q[Topic: notification_events] --> O
        R[Topic: analytics_events] --> O
        S[Topic: system_events] --> O
    end
    
    subgraph "消费者服务层"
        T[消息处理消费者] --> U[通知推送消费者]
        U --> V[数据分析消费者]
        V --> W[系统监控消费者]
    end
    
    subgraph "数据存储层"
        X[MySQL数据库] --> Y[Redis缓存]
        Z[ElasticSearch] --> AA[监控数据库]
    end
    
    A --> C
    B --> C
    C --> G
    G --> X
    H --> X
    I --> X
    J --> X
    K --> X
    
    I --> N
    J --> N
    K --> N
    L --> N
    
    N --> P
    N --> Q
    N --> R
    N --> S
    
    P --> T
    Q --> U
    R --> V
    S --> W
    
    T --> X
    T --> Y
    U --> Y
    V --> Z
    W --> AA
```

### 技术栈

- **Web框架**: Gin
- **ORM**: GORM
- **关系数据库**: MySQL (帖子、会话、消息、用户关系)
- **缓存**: Redis (会话状态、未读计数、消息队列缓存)
- **消息队列**: Kafka (异步消息处理、系统解耦、事件驱动)
- **搜索引擎**: ElasticSearch (消息搜索、数据分析)
- **监控**: Prometheus + Grafana (系统监控、告警)
- **文件存储**: 腾讯云COS

### 通信模式设计

#### HTTP轮询 vs 实时通信

系统采用**HTTP请求响应模式**而非WebSocket实时通信，基于以下设计考量：

**设计决策理由**：
1. **简化架构**: 避免WebSocket连接管理的复杂性，减少服务器资源消耗
2. **更好的扩展性**: HTTP无状态特性便于负载均衡和水平扩展
3. **移动端友好**: 避免长连接在移动网络环境下的稳定性问题
4. **业务特性匹配**: 卡牌交易沟通不需要毫秒级实时性，秒级延迟可接受

**实现方式**：
- 客户端通过定时轮询获取新消息
- 服务端通过推送通知提醒用户有新消息
- 关键状态变更通过Kafka异步处理，确保数据一致性

#### HTTP请求模式详细说明

**消息获取流程**：
```
1. 客户端定时发送 GET /web/v1/conversations/{id}/messages
2. 服务端返回最新消息列表（分页）
3. 客户端自动更新最后阅读时间
4. 服务端通过推送通知告知有新消息
```

**优势**：
- **可靠性**: HTTP请求失败可重试，状态可追踪
- **调试友好**: 标准HTTP协议，易于监控和调试
- **缓存支持**: 可利用HTTP缓存机制优化性能
- **防火墙友好**: 标准HTTP端口，无连接管理问题

### 消息ID和时间戳生成策略

#### 设计原则

基于分布式系统最佳实践和Apache Kafka消息处理模式，系统采用**混合生成策略**：

**消息ID生成 (client_msg_id)**：
- **生成方**: 客户端
- **目的**: 实现幂等性，防止重复发送
- **格式**: UUID或时间戳+随机数
- **唯一性保证**: 数据库唯一约束 `uk_client_msg_id`

**逻辑时间戳生成 (logical_timestamp)**：
- **生成方**: 服务端
- **目的**: 确保消息全局有序性
- **实现**: 单调递增的长整型数值
- **排序依据**: 用于消息列表的准确排序

**设计理由**：
```go
// 客户端生成消息ID确保幂等性
type SendMessageRequest struct {
    ClientMsgID string `json:"client_msg_id"` // 客户端生成，防重复
    // ... 其他字段
}

// 服务端生成逻辑时间戳确保排序
type Message struct {
    ID               string `json:"id"`
    ClientMsgID      string `json:"client_msg_id"`      // 客户端生成
    LogicalTimestamp int64  `json:"logical_timestamp"`  // 服务端生成
    CreatedAt        time.Time `json:"created_at"`
}
```

**参考Apache Kafka最佳实践**：
- 客户端负责去重标识（类似Kafka的幂等生产者机制）
- 服务端负责顺序保证（类似Kafka的offset排序机制）
- 混合策略确保既有幂等性又有全局一致的排序

#### 去重和排序机制

**幂等性实现**：
```sql
-- 数据库层面防重复
UNIQUE KEY `uk_client_msg_id` (`client_msg_id`)

-- 应用层面检查
SELECT id FROM messages WHERE client_msg_id = ?
```

**排序一致性**：
```sql
-- 按逻辑时间戳排序，确保全局有序
SELECT * FROM messages 
WHERE small_user_id = ? AND big_user_id = ?
ORDER BY logical_timestamp ASC
```

### 消息队列设计

#### Kafka Topic设计

##### 1. 消息事件Topic (message_events)

**用途**: 处理所有消息相关的事件

**分区策略**: 按用户对(big_user_id + small_user_id)分区，确保同一对用户的消息有序处理

**事件类型**:
- `message.sent`: 消息发送事件
- `message.read`: 消息已读事件
- `message.deleted`: 消息删除事件

**消息格式**:
```json
{
  "event_id": "msg_evt_123456",
  "event_type": "message.sent",
  "message_id": "msg_123456",
  "conversation_id": "conv_123456",
  "sender_id": "user_123",
  "receiver_id": "user_456",
  "message_type": 1,
  "content": "消息内容摘要",
  "timestamp": "2025-01-25T10:30:00Z",
  "metadata": {
    "client_msg_id": "client_123456",
    "direction": 0,
    "is_smart_reply": false
  }
}
```

##### 2. 通知事件Topic (notification_events)

**用途**: 处理所有通知推送相关的事件

**分区策略**: 按用户ID分区，确保同一用户的通知有序处理

**事件类型**:
- `notification.push`: 推送通知事件
- `notification.email`: 邮件通知事件
- `notification.sms`: 短信通知事件

**消息格式**:
```json
{
  "event_id": "notify_evt_123456",
  "event_type": "notification.push",
  "user_id": "user_123",
  "title": "新消息通知",
  "content": "您收到了一条新消息",
  "notify_type": 1,
  "priority": 2,
  "timestamp": "2025-01-25T10:30:00Z",
  "metadata": {
    "conversation_id": "conv_123456",
    "sender_name": "商家名称",
    "message_preview": "消息预览"
  }
}
```

##### 3. 数据分析事件Topic (analytics_events)

**用途**: 处理所有数据分析和统计相关的事件

**分区策略**: 按事件类型分区，便于不同类型的分析处理

**事件类型**:
- `analytics.user_action`: 用户行为分析
- `analytics.business_metric`: 业务指标统计
- `analytics.performance_metric`: 性能指标统计

**消息格式**:
```json
{
  "event_id": "analytics_evt_123456",
  "event_type": "analytics.user_action",
  "user_id": "user_123",
  "action": "send_message",
  "resource": "message",
  "resource_id": "msg_123456",
  "properties": {
    "conversation_id": "conv_123456",
    "message_type": 1,
    "response_time": 150,
    "device_type": "mobile"
  },
  "timestamp": "2025-01-25T10:30:00Z"
}
```

##### 4. 系统事件Topic (system_events)

**用途**: 处理系统监控、告警和日志相关的事件

**分区策略**: 按服务名称分区，便于不同服务的监控处理

**事件类型**:
- `system.alert`: 系统告警事件
- `system.performance`: 性能监控事件
- `system.error`: 错误日志事件

**消息格式**:
```json
{
  "event_id": "system_evt_123456",
  "event_type": "system.alert",
  "service": "message_service",
  "level": "ERROR",
  "message": "数据库连接超时",
  "details": {
    "error_code": "DB_TIMEOUT",
    "query": "SELECT * FROM messages WHERE...",
    "duration": 5000,
    "retry_count": 3
  },
  "timestamp": "2025-01-25T10:30:00Z"
}
```

#### 消费者组设计

##### 1. 消息处理消费者组 (message-processor-group)

**订阅Topic**: message_events

**处理逻辑**:
- 更新会话状态和未读计数
- 触发智能回复逻辑
- 更新缓存数据
- 记录消息统计

**并发配置**: 16个消费者实例，与分区数匹配

##### 2. 通知推送消费者组 (notification-sender-group)

**订阅Topic**: notification_events

**处理逻辑**:
- 发送推送通知
- 发送邮件通知
- 发送短信通知
- 记录通知状态

**并发配置**: 8个消费者实例，支持高并发推送

##### 3. 数据分析消费者组 (analytics-processor-group)

**订阅Topic**: analytics_events

**处理逻辑**:
- 实时数据统计
- 用户行为分析
- 业务指标计算
- 数据写入ElasticSearch

**并发配置**: 4个消费者实例，批量处理分析数据

##### 4. 系统监控消费者组 (system-monitor-group)

**订阅Topic**: system_events

**处理逻辑**:
- 系统告警处理
- 性能指标收集
- 错误日志分析
- 监控数据存储

**并发配置**: 2个消费者实例，处理系统监控数据

#### 消息队列配置

##### Kafka集群配置

```yaml
# Kafka集群配置
kafka:
  brokers:
    - kafka-1:9092
    - kafka-2:9092
    - kafka-3:9092
  
  # Topic配置
  topics:
    message_events:
      partitions: 16
      replication_factor: 3
      retention_ms: 604800000  # 7天
      
    notification_events:
      partitions: 8
      replication_factor: 3
      retention_ms: 259200000  # 3天
      
    analytics_events:
      partitions: 4
      replication_factor: 3
      retention_ms: 2592000000  # 30天
      
    system_events:
      partitions: 2
      replication_factor: 3
      retention_ms: 1209600000  # 14天

  # 生产者配置
  producer:
    acks: all
    retries: 3
    batch_size: 16384
    linger_ms: 5
    compression_type: snappy
    
  # 消费者配置
  consumer:
    auto_offset_reset: earliest
    enable_auto_commit: false
    max_poll_records: 500
    session_timeout_ms: 30000
```

#### 消息队列监控

##### 关键监控指标

```go
// Kafka监控指标
type KafkaMetrics struct {
    // 生产者指标
    ProducerMetrics struct {
        MessagesPerSec    float64 `json:"messages_per_sec"`    // 每秒消息数
        BytesPerSec       float64 `json:"bytes_per_sec"`       // 每秒字节数
        FailedSends       int64   `json:"failed_sends"`        // 发送失败数
        AvgLatency        float64 `json:"avg_latency_ms"`      // 平均延迟
    } `json:"producer_metrics"`
    
    // 消费者指标
    ConsumerMetrics struct {
        MessagesPerSec    float64 `json:"messages_per_sec"`    // 每秒消费数
        LagTotal          int64   `json:"lag_total"`           // 总消费延迟
        AvgProcessTime    float64 `json:"avg_process_time_ms"` // 平均处理时间
        FailedProcesses   int64   `json:"failed_processes"`    // 处理失败数
    } `json:"consumer_metrics"`
    
    // Topic指标
    TopicMetrics map[string]struct {
        MessageCount      int64   `json:"message_count"`       // 消息总数
        BytesIn           int64   `json:"bytes_in"`            // 输入字节数
        BytesOut          int64   `json:"bytes_out"`           // 输出字节数
        PartitionCount    int     `json:"partition_count"`     // 分区数
    } `json:"topic_metrics"`
}
```

##### 告警规则

```yaml
# Kafka告警规则
- name: kafka_alerts
  rules:
    - alert: KafkaProducerHighLatency
      expr: kafka_producer_avg_latency_ms > 1000
      for: 2m
      labels:
        severity: warning
        
    - alert: KafkaConsumerLagHigh
      expr: kafka_consumer_lag_total > 10000
      for: 5m
      labels:
        severity: critical
        
    - alert: KafkaMessageSendFailed
      expr: increase(kafka_producer_failed_sends[5m]) > 100
      for: 1m
      labels:
        severity: critical
        
    - alert: KafkaConsumerProcessFail

## 组件和接口

### 核心组件

#### 1. 帖子管理组件 (PostManager)

```go
type PostManager interface {
    CreatePost(ctx context.Context, req *CreatePostRequest) (*Post, error)
    GetPostList(ctx context.Context, req *GetPostListRequest) (*PostListResponse, error)
    GetPostDetail(ctx context.Context, post_id string) (*Post, error)
    UpdatePostStatus(ctx context.Context, post_id string, status PostStatus) error
    DeletePost(ctx context.Context, post_id string) error
}
```

#### 2. 会话管理组件 (ConversationManager)

```go
type ConversationManager interface {
    CreateOrGetConversation(ctx context.Context, user_id, merchant_id string) (*Conversation, error)
    GetConversationList(ctx context.Context, user_id string, pagination *Pagination) (*ConversationListResponse, error)
    GetConversation(ctx context.Context, conversation_id string) (*Conversation, error)
    UpdateLastReadTime(ctx context.Context, conversation_id, user_id string) error
    
    // MarkConversationAsRead 标记会话为已读
    MarkConversationAsRead(ctx context.Context, req *MarkAsReadRequest) (*MarkAsReadResponse, error)
}

// MarkAsReadRequest 标记已读请求
type MarkAsReadRequest struct {
    ConversationID string    `json:"conversation_id" binding:"required"` // 会话ID
    UserID         string    `json:"user_id" binding:"required"`         // 用户ID
    ReadTime       time.Time `json:"read_time"`                          // 阅读时间，可选，默认为当前时间
}

// MarkAsReadResponse 标记已读响应
type MarkAsReadResponse struct {
    Success       bool `json:"success"`        // 操作是否成功
    UnreadCount   int  `json:"unread_count"`   // 更新后的未读消息数量
}


```

#### 3. 消息管理组件 (MessageManager)

```go
type MessageManager interface {
    // SendMessage 发送消息并同时更新会话的最后消息信息
    // 1. 检查client_msg_id是否已存在，实现幂等性
    // 2. 在MySQL单表中创建新的消息记录
    // 3. 更新conversations表的最后消息信息
    SendMessage(ctx context.Context, req *SendMessageRequest) (*Message, error)
    
    // GetMessageList 获取消息列表（单表查询）
    // 1. 根据big_user_id和small_user_id查询消息
    // 2. 支持分页和时间范围查询
    GetMessageList(ctx context.Context, bigUserID, smallUserID string, pagination *Pagination) (*MessageListResponse, error)
    
    // GetUnreadCount 获取未读消息数量（基于conversations表的最后阅读时间计算）
    GetUnreadCount(ctx context.Context, conversationID, userID string) (int, error)
    
    // GetMessageByClientID 根据客户端消息ID查询消息
    GetMessageByClientID(ctx context.Context, clientMsgID string) (*Message, error)
}

// SendMessageRequest 发送消息请求结构
type SendMessageRequest struct {
    ClientMsgID    string      `json:"client_msg_id" binding:"required"`    // 客户端生成的消息ID
    ConversationID string      `json:"conversation_id" binding:"required"`  // 会话ID
    SenderID       string      `json:"sender_id" binding:"required"`        // 发送者ID
    SenderType     SenderType  `json:"sender_type" binding:"required"`      // 发送者类型
    MessageType    MessageType `json:"message_type" binding:"required"`     // 消息类型
    Content        string      `json:"content"`                             // 消息内容
    MediaFile      *MediaFile  `json:"media_file"`                          // 媒体文件
    PostID         string      `json:"post_id"`                             // 关联帖子ID（帖子消息类型）
}
```

#### 4. 智能回复组件 (SmartReplyManager)

```go
type SmartReplyManager interface {
    GetTemplate(ctx context.Context, merchantID string) (*SmartReplyTemplate, error)
    UpdateTemplate(ctx context.Context, req *UpdateTemplateRequest) error
    ToggleSmartReply(ctx context.Context, merchantID string, enabled bool) error
    SendSmartReply(ctx context.Context, conversationID string) error
}
```

#### 5. 管理端组件 (AdminManager)

```go
type AdminManager interface {
    // 求购管理
    GetPostsForAdmin(ctx context.Context, req *AdminPostListRequest) (*AdminPostListResponse, error)
    GetPostDetailForAdmin(ctx context.Context, postID string) (*AdminPostDetail, error)
    UpdatePostStatusByAdmin(ctx context.Context, postID string, status PostStatus) error
    
    // 会话管理
    GetConversationsForAdmin(ctx context.Context, req *AdminConversationListRequest) (*AdminConversationListResponse, error)
    GetConversationDetailForAdmin(ctx context.Context, conversationID string) (*AdminConversationDetail, error)
    
    // 商家管理
    GetMerchantApplications(ctx context.Context, req *MerchantApplicationListRequest) (*MerchantApplicationListResponse, error)
    ApproveMerchantApplication(ctx context.Context, applicationID string, approved bool) error
}
```

#### 6. 消息队列服务组件 (MessageQueueService)

```go
type MessageQueueService interface {
    // 消息事件发布
    PublishMessageEvent(ctx context.Context, event *MessageEvent) error
    
    // 通知事件发布
    PublishNotificationEvent(ctx context.Context, event *NotificationEvent) error
    
    // 数据分析事件发布
    PublishAnalyticsEvent(ctx context.Context, event *AnalyticsEvent) error
    
    // 系统事件发布
    PublishSystemEvent(ctx context.Context, event *SystemEvent) error
}

// 消息事件结构
type MessageEvent struct {
    EventID       string    `json:"event_id"`       // 事件ID
    EventType     string    `json:"event_type"`     // 事件类型：message_sent, message_read
    MessageID     string    `json:"message_id"`     // 消息ID
    ConversationID string   `json:"conversation_id"` // 会话ID
    SenderID      string    `json:"sender_id"`      // 发送者ID
    ReceiverID    string    `json:"receiver_id"`    // 接收者ID
    MessageType   int       `json:"message_type"`   // 消息类型
    Content       string    `json:"content"`        // 消息内容摘要
    Timestamp     time.Time `json:"timestamp"`      // 事件时间戳
    Metadata      map[string]interface{} `json:"metadata"` // 扩展元数据
}

// 通知事件结构
type NotificationEvent struct {
    EventID      string    `json:"event_id"`       // 事件ID
    EventType    string    `json:"event_type"`     // 事件类型：push_notification, email_notification
    UserID       string    `json:"user_id"`        // 目标用户ID
    Title        string    `json:"title"`          // 通知标题
    Content      string    `json:"content"`        // 通知内容
    NotifyType   int       `json:"notify_type"`    // 通知类型：1=推送 2=邮件 3=短信
    Priority     int       `json:"priority"`       // 优先级：1=低 2=中 3=高
    Timestamp    time.Time `json:"timestamp"`      // 事件时间戳
    Metadata     map[string]interface{} `json:"metadata"` // 扩展元数据
}

// 数据分析事件结构
type AnalyticsEvent struct {
    EventID      string    `json:"event_id"`       // 事件ID
    EventType    string    `json:"event_type"`     // 事件类型：user_action, business_metric
    UserID       string    `json:"user_id"`        // 用户ID
    Action       string    `json:"action"`         // 用户行为
    Resource     string    `json:"resource"`       // 资源类型
    ResourceID   string    `json:"resource_id"`    // 资源ID
    Properties   map[string]interface{} `json:"properties"` // 事件属性
    Timestamp    time.Time `json:"timestamp"`      // 事件时间戳
}

// 系统事件结构
type SystemEvent struct {
    EventID      string    `json:"event_id"`       // 事件ID
    EventType    string    `json:"event_type"`     // 事件类型：system_alert, performance_metric
    Service      string    `json:"service"`        // 服务名称
    Level        string    `json:"level"`          // 日志级别：INFO, WARN, ERROR
    Message      string    `json:"message"`        // 事件消息
    Details      map[string]interface{} `json:"details"` // 详细信息
    Timestamp    time.Time `json:"timestamp"`      // 事件时间戳
}
```

#### 7. 消息消费者组件 (MessageConsumers)

```go
// 消息处理消费者
type MessageEventConsumer interface {
    // 处理消息发送事件
    HandleMessageSent(ctx context.Context, event *MessageEvent) error
    
    // 处理消息已读事件
    HandleMessageRead(ctx context.Context, event *MessageEvent) error
    
    // 更新会话状态
    UpdateConversationStatus(ctx context.Context, event *MessageEvent) error
}

// 通知推送消费者
type NotificationConsumer interface {
    // 处理推送通知
    HandlePushNotification(ctx context.Context, event *NotificationEvent) error
    
    // 处理邮件通知
    HandleEmailNotification(ctx context.Context, event *NotificationEvent) error
    
    // 处理短信通知
    HandleSMSNotification(ctx context.Context, event *NotificationEvent) error
}

// 数据分析消费者
type AnalyticsConsumer interface {
    // 处理用户行为分析
    HandleUserAction(ctx context.Context, event *AnalyticsEvent) error
    
    // 处理业务指标统计
    HandleBusinessMetric(ctx context.Context, event *AnalyticsEvent) error
    
    // 更新实时统计
    UpdateRealTimeStats(ctx context.Context, event *AnalyticsEvent) error
}

// 系统监控消费者
type SystemMonitorConsumer interface {
    // 处理系统告警
    HandleSystemAlert(ctx context.Context, event *SystemEvent) error
    
    // 处理性能指标
    HandlePerformanceMetric(ctx context.Context, event *SystemEvent) error
    
    // 更新监控数据
    UpdateMonitoringData(ctx context.Context, event *SystemEvent) error
}
```

## 数据模型

### UML类图

```mermaid
classDiagram
    class Post {
        +string ID
        +string MerchantID
        +string Title
        +string Description
        +int64 Price
        +MediaFile[] MediaFiles
        +PostStatus Status
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +CreatePost()
        +UpdateStatus()
        +Delete()
    }
    
    class Conversation {
        +string ID
        +string UserID
        +string MerchantID
        +string Title
        +string LastMessageID
        +string LastMessageContent
        +DateTime LastMessageTime
        +DateTime UserLastReadTime
        +DateTime MerchantLastReadTime
        +ConversationStatus Status
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +Create()
        +UpdateLastReadTime()
        +GetUnreadCount()
        +UpdateLastMessageInfo(message)
        +GetLastMessageInfo()
    }
    
    class Message {
        +string ID
        +string ClientMsgID
        +string ConversationID
        +string BigUserID
        +string SmallUserID
        +int Direction
        +string SenderID
        +SenderType SenderType
        +MessageType MessageType
        +string Content
        +MediaFile Media
        +bool IsSmartReply
        +DateTime CreatedAt
        +Send()
        +GetList()
        +UpdateConversationLastMessage()
    }
    
    class SmartReplyTemplate {
        +string ID
        +string MerchantID
        +string TemplateName
        +string TemplateContent
        +bool IsDefault
        +bool IsEnabled
        +int UsageCount
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +Update()
        +Toggle()
    }
    
    class User {
        +string ID
        +string Name
        +string Avatar
        +UserType UserType
        +DateTime CreatedAt
    }
    
    class MediaFile {
        +string URL
        +MediaType Type
        +int Size
        +int Width
        +int Height
        +int Duration
        +string ThumbnailURL
    }
    
    class MerchantApplication {
        +string ID
        +string UserID
        +string BusinessName
        +string ContactInfo
        +ApplicationStatus Status
        +string ReviewReason
        +string ReviewerID
        +DateTime AppliedAt
        +DateTime ReviewedAt
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +Apply()
        +Review()
    }
    
    class AdminOperationLog {
        +string ID
        +string AdminID
        +string TargetType
        +string TargetID
        +string Operation
        +string Reason
        +DateTime CreatedAt
        +Log()
    }
    
    User --> Post
    User --> Conversation
    Conversation "1" <--> "many" Message : 一条会话记录两条数据
    User --> SmartReplyTemplate
    User --> Message
    Post --> MediaFile
    User --> MerchantApplication
    User --> AdminOperationLog
    Message --> MediaFile
```

### 数据库设计

#### MySQL表结构

**posts表**
```sql
CREATE TABLE `posts` (
    `id` VARCHAR(64) NOT NULL COMMENT '帖子ID',
    `merchant_id` VARCHAR(64) NOT NULL COMMENT '商家ID',
    -- `title` VARCHAR(200) NOT NULL COMMENT '帖子标题',
    `description` TEXT COMMENT '帖子描述',
    `price` BIGINT NOT NULL COMMENT '收购价格(分)',
    `media_files` JSON COMMENT '媒体文件数组，格式:[{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}]',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=已上架 -1=已删除 -2=已下架 -3=违规下架',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='求购帖子表';
```

**conversations表（双向记录设计）**
```sql
CREATE TABLE `conversations` (
    `id` varchar(64) NOT NULL COMMENT '会话记录ID',
    --  `conversation_id` varchar(64) NOT NULL COMMENT '会话ID（双方共享）',
    `participant_id` varchar(64) NOT NULL COMMENT '参与者ID（用户ID或商家ID）',
    `participant_type` tinyint NOT NULL COMMENT '参与者类型：1=用户 2=商家',
    `other_participant_id` varchar(64) NOT NULL COMMENT '对方ID',
    `title` varchar(100) DEFAULT NULL COMMENT '会话标题',
    `last_message_id` varchar(64) DEFAULT NULL COMMENT '最后消息ID',
    `last_message_content` varchar(500) DEFAULT NULL COMMENT '最后消息内容',
    `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
    `last_message_type` tinyint DEFAULT NULL COMMENT '最后消息类型：1=文本 2=图片 3=帖子快照',
    `last_read_time` datetime DEFAULT NULL COMMENT '最后阅读时间',
    `unread_count` int NOT NULL DEFAULT 0 COMMENT '未读消息数',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除：0=正常 1=已删除',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_conversation_participant` (`participant_id`, `other_participant_id`),
    KEY `idx_participant_id` (`participant_id`),
    KEY `idx_last_message_time` (`last_message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表（双向记录）';
```

```sql
CREATE TABLE `messages` (
    `id` varchar(64) NOT NULL COMMENT '消息ID',
    `client_msg_id` varchar(64) NOT NULL COMMENT '客户端生成消息ID，用于幂等性控制',
    `client_msg_number` bigint DEFAULT NULL COMMENT '客户端生成消息序号，用于消息排序',
    -- `conversation_id` varchar(64) NOT NULL COMMENT '会话ID',
    `small_user_id` varchar(64) NOT NULL COMMENT '较小的用户ID min(sender_id, receiver_id)',
    `big_user_id` varchar(64) NOT NULL COMMENT '较大的用户ID max(sender_id, receiver_id)',
    `direction` tinyint NOT NULL COMMENT '消息方向：-1=较大uid向较小uid发送消息，1=较小uid向较大uid发送消息',
    -- `sender_id` varchar(64) NOT NULL COMMENT '发送者ID',
    -- `sender_type` tinyint NOT NULL COMMENT '发送者类型：1=用户 2=商家 3=系统',
    `message_type` tinyint NOT NULL COMMENT '消息类型：1=文本 2=图片 3=帖子快照',
    `content` text COMMENT '消息内容',
    `media_file` json COMMENT '媒体文件信息，格式:{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}',
    `post_id` varchar(64) DEFAULT NULL COMMENT '关联的帖子ID（帖子消息类型时使用）',
    `post_snapshot` json DEFAULT NULL COMMENT '帖子快照信息，格式:{"description":"文案","price":100.00,"media_files":[...]}',
    `is_smart_reply` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否智能回复：0=否 1=是',
    `app_channel` varchar(20) DEFAULT NULL COMMENT '应用渠道',
    `app_version` varchar(20) DEFAULT NULL COMMENT '应用版本号',
    `client_type` varchar(20) DEFAULT NULL COMMENT '客户端类型',
    `ip` varchar(255) DEFAULT NULL COMMENT '发送方IP地址',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_client_msg_id` (`client_msg_id`),
    -- KEY `idx_conversation_id` (`conversation_id`),
    KEY `idx_user_pair` (`small_user_id`,`big_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

```


**smart_reply_templates表**
```sql
CREATE TABLE `smart_reply_templates` (
    `id` VARCHAR(64) NOT NULL COMMENT '模板ID',
    `merchant_id` VARCHAR(64) NOT NULL COMMENT '商家ID',
    -- `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `template_content` TEXT NOT NULL COMMENT '模板内容',
    -- `is_default` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认模板',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    -- `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能回复模板表';
```

**merchant_applications表**
```sql
CREATE TABLE `merchant_applications` (
    `id` VARCHAR(64) NOT NULL COMMENT '申请ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=待审核 2=审核通过 -1=审核不通过',
    -- `review_reason` VARCHAR(500) DEFAULT NULL COMMENT '审核原因',
    `reviewer_id` VARCHAR(64) DEFAULT NULL COMMENT '审核人ID',
    `applied_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    `reviewed_at` DATETIME DEFAULT NULL COMMENT '审核时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家申请表';
```




### 会话表双向记录设计（一条会话记录两条数据）

系统采用"一条会话记录两条数据"的设计模式来存储会话信息：

#### 设计原理

1. **双向记录机制**：
   - 每个会话在conversations表中创建两条记录
   - 一条记录代表用户端视角（participant_type=1）
   - 一条记录代表商家端视角（participant_type=2）
   - 两条记录共享相同的conversation_id

2. **独立状态管理**：
   - 每条记录维护独立的last_read_time（最后阅读时间）
   - 每条记录维护独立的unread_count（未读消息数）
   - 每条记录维护独立的is_deleted（删除状态）
   - 支持单方删除联系人而不影响对方

3. **索引优化设计**：
   - 主索引：participant_id，支持快速查询某用户/商家的所有会话
   - 查询SQL：`WHERE participant_id = ? AND is_deleted = 0`
   - 避免了复杂的OR条件查询，提高查询性能

4. **分片预留设计**：
   - **分片键选择**：participant_id作为分片键，确保同一用户的所有会话在同一分片
   - **分片算法预留**：`hash(participant_id) % shard_count`
   - **查询优化**：单用户会话查询无需跨分片，性能最优
   - **扩展触发**：当conversations表记录数超过100万或单表查询性能下降时启用分片

#### 消息存储机制

1. **MySQL单表存储完整消息内容**：
   - 每条消息完整存储在MySQL的messages表中
   - 包含消息的全部内容、媒体信息、发送者信息等
   - 通过索引优化支持高效的消息查询
   - 利用MySQL的事务特性保证数据一致性

2. **conversations表存储会话元数据和最后消息摘要**：
   - 在conversations表中存储会话的元数据
   - 同时保存最后一条消息的ID、内容摘要和时间
   - 便于快速查询会话列表和计算未读状态
   - 统一的MySQL存储架构，简化了数据一致性管理

#### 设计优势

- **业务逻辑支持**：支持已读状态、删除联系人、未读计数等功能
- **索引性能优化**：participant_id索引支持高效的单方查询
- **分离存储**：提高了系统的扩展性和性能
- **减少复杂查询**：避免了复杂的联合查询和OR条件

#### 消息幂等性和方向设计

**client_msg_id字段设计**：
- **幂等性保证**：客户端生成唯一的消息ID，防止网络异常导致的重复发送
- **唯一性约束**：在数据库层面添加唯一索引，确保同一条消息不会被重复存储
- **客户端实现**：客户端可使用UUID或时间戳+随机数的方式生成唯一ID
- **重试机制**：当消息发送失败时，客户端可使用相同的client_msg_id重试，服务端会识别并返回成功

**direction字段设计**：
- **统一方向标识**：0表示较大uid向较小uid发送，1表示相反方向
- **查询优化**：结合big_user_id和small_user_id，可以快速定位消息方向
- **存储一致性**：避免了复杂的发送者/接收者判断逻辑，统一了消息存储格式
- **索引支持**：为direction字段添加索引，支持按方向快速查询消息
```

### 枚举定义

```go
// PostStatus 帖子状态
type PostStatus int
const (
    PostStatusViolation PostStatus = -3 //违规下架
    PostStatusInactive PostStatus = -2 // 已下架
    PostStatusDeleted  PostStatus = -1 // 已删除
    PostStatusActive   PostStatus = 1 // 已上架
)

// MediaType 媒体类型
type MediaType int
const (
    MediaTypeImage MediaType = 1 // 图片
    MediaTypeVideo MediaType = 2 // 视频
)

// MessageType 消息类型
type MessageType int
const (
    MessageTypeText  MessageType = 1 // 文本
    MessageTypeImage MessageType = 2 // 图片
    MessageTypePost  MessageType = 3 // 帖子快照
)

// SenderType 发送者类型
type SenderType int
const (
    SenderTypeUser     SenderType = 1 // 用户
    SenderTypeMerchant SenderType = 2 // 商家
    SenderTypeSystem   SenderType = 3 // 系统
)

// MessageDirection 消息方向
type MessageDirection int
const (
    MessageDirectionBigToSmall MessageDirection = 0 // 较大uid向较小uid发送消息
    MessageDirectionSmallToBig MessageDirection = 1 // 较小uid向较大uid发送消息
)

// UserType 用户类型
type UserType int
const (
    UserTypeNormal   UserType = 1 // 普通用户
    UserTypeMerchant UserType = 2 // 商家
    UserTypeAdmin    UserType = 3 // 管理员
)

// ApplicationStatus 申请状态
type ApplicationStatus int
const (
    ApplicationStatusRejected ApplicationStatus = -1 // 审核不通过
    ApplicationStatusPending  ApplicationStatus = 1  // 待审核
    ApplicationStatusApproved ApplicationStatus = 2  // 审核通过
)


// MediaFile 媒体文件结构
type MediaFile struct {
    URL         string    `json:"url"`          // 文件URL
    Type        MediaType `json:"type"`         // 媒体类型
    Size        int64     `json:"size"`         // 文件大小(字节)
    Width       int       `json:"width"`        // 宽度(像素)
    Height      int       `json:"height"`       // 高度(像素)
    Duration    int       `json:"duration"`     // 视频时长(秒)，图片为0
    ThumbnailURL string   `json:"thumbnail_url"` // 缩略图URL
}
```

## 性能优化和扩展性设计

### 单表设计方案

#### 方案概述

当前版本采用单库单表的简化设计，便于快速开发和部署。系统保留了扩展性设计，当数据量增长到一定规模时，可以平滑升级为分库分表架构。

#### 单表优化策略

##### 1. 索引设计

**主要查询索引**:
```sql
-- 用户对消息查询索引（最重要）
KEY `idx_user_pair_time` (`big_user_id`, `small_user_id`, `created_at`),

-- 客户端消息ID唯一索引（幂等性）
UNIQUE KEY `uk_client_msg_id` (`client_msg_id`),

-- 消息方向查询索引
KEY `idx_direction` (`direction`),

-- 时间范围查询索引
KEY `idx_created_at` (`created_at`),

-- 消息类型查询索引
KEY `idx_message_type` (`message_type`)
```

##### 2. 查询优化

**场景1: 查询两个用户之间的消息历史**
```go
// 优化：使用复合索引，单表查询
func GetConversationMessages(bigUserID, smallUserID string, pagination *Pagination) {
    // SELECT * FROM messages 
    // WHERE big_user_id = ? AND small_user_id = ? 
    // ORDER BY created_at DESC 
    // LIMIT ? OFFSET ?
}
```

**场景2: 消息已读状态更新**
```go
// 优化：已读状态更新只涉及conversations表
func MarkConversationAsRead(userID, conversationID string, readTime time.Time) {
    // UPDATE conversations SET last_read_time = ? 
    // WHERE participant_id = ? AND id = ?
    
    // 重新计算未读数量
    unreadCount := calculateUnreadCount(userID, conversationID)
    
    // 更新缓存
    updateUnreadCountCache(userID, conversationID, unreadCount)
}
```

**场景3: 幂等性检查**
```go
// 优化：利用数据库唯一约束进行幂等性检查
func SendMessage(ctx context.Context, req *SendMessageRequest) error {
    // 直接插入，利用uk_client_msg_id唯一约束
    err := insertMessage(ctx, req)
    if isDuplicateKeyError(err) {
        return ErrMessageDuplicate
    }
    return err
}
```

#### 性能评估

##### 1. 容量规划

**单表容量**: 预计支持1000万条消息（约15GB存储）
**扩展阈值**: 当消息量达到500万条或存储超过10GB时，考虑分库分表

**存储空间估算**:
- 平均每条消息: 1.5KB (包含完整消息内容和媒体信息)
- 1000万条消息: 1000万 × 1.5KB = 15GB
- 索引空间: 约3GB
- 总存储需求: 约18GB

##### 2. 性能指标

**查询性能**:
- 用户对消息查询: < 50ms (单表+索引优化)
- 用户会话列表查询: < 100ms (conversations表查询)
- 消息发送: < 30ms (单表写入)

**并发能力**:
- 写入QPS: 2000-3000
- 读取QPS: 10000-15000
- 支持并发用户数: 10000+

#### 扩展性设计

##### 1. 分库分表预留设计

**Messages表字段预留**:
- `big_user_id` 和 `small_user_id`: 为分片路由预留
- `direction`: 为消息方向查询预留
- `client_msg_id`: 为跨分片幂等性预留

**Conversations表字段预留**:
- `participant_id`: 作为分片键，确保用户会话在同一分片
- `participant_type`: 区分用户类型，支持不同类型的分片策略
- `other_participant_id`: 为关联查询优化预留

**代码架构预留**:
```go
// 消息管理接口设计时考虑分片扩展
type MessageManager interface {
    // 当前单表实现
    SendMessage(ctx context.Context, req *SendMessageRequest) (*Message, error)
    GetMessageList(ctx context.Context, bigUserID, smallUserID string, pagination *Pagination) (*MessageListResponse, error)
    
    // 未来分片扩展时，接口保持不变，内部实现路由逻辑
}

// 会话管理接口设计时考虑分片扩展
type ConversationManager interface {
    // 当前单表实现
    GetConversationList(ctx context.Context, userID string, pagination *Pagination) (*ConversationListResponse, error)
    MarkConversationAsRead(ctx context.Context, req *MarkAsReadRequest) (*MarkAsReadResponse, error)
    
    // 未来分片扩展时，接口保持不变，内部根据participant_id路由
}
```

##### 2. 扩展触发条件

**Messages表触发条件**:
- 消息表记录数 > 500万条
- 单表存储空间 > 10GB
- 查询响应时间 > 100ms

**Conversations表触发条件**:
- 会话表记录数 > 100万条
- 单表存储空间 > 2GB
- 用户会话列表查询响应时间 > 50ms

**业务量触发**:
- 日活用户 > 50000
- 日消息量 > 100万条
- 并发写入QPS > 2000
- 并发会话查询QPS > 5000

##### 3. 平滑扩展方案

**阶段1: 准备阶段**
1. 创建分片数据库和表结构
   - Messages表：按用户对分片
   - Conversations表：按participant_id分片
2. 实现分片路由中间件
   - MessageRouter：处理消息表路由
   - ConversationRouter：处理会话表路由
3. 部署数据迁移工具

**阶段2: 数据迁移**
1. 停止写入服务
2. 将历史数据按分片规则迁移
   - Messages：根据big_user_id+small_user_id分片
   - Conversations：根据participant_id分片
3. 验证数据完整性和关联关系

**阶段3: 切换上线**
1. 更新应用配置，启用分片路由
2. 恢复写入服务
3. 监控系统运行状态

**分片路由设计预览**:
```go
// 会话表分片路由器
type ConversationRouter struct {
    shards map[int]*sql.DB
}

func (r *ConversationRouter) GetShard(participantID string) *sql.DB {
    shardIndex := hash(participantID) % len(r.shards)
    return r.shards[shardIndex]
}

// 消息表分片路由器  
type MessageRouter struct {
    shards map[int]*sql.DB
}

func (r *MessageRouter) GetShard(bigUserID, smallUserID string) *sql.DB {
    key := bigUserID + "_" + smallUserID
    shardIndex := hash(key) % len(r.shards)
    return r.shards[shardIndex]
}
```

#### 监控和运维

##### 1. 关键监控指标

```go
// Messages表监控指标
type MessageTableMetrics struct {
    TableName       string  `json:"table_name"`           // 表名
    TableSize       int64   `json:"table_size_mb"`        // 表大小(MB)
    RecordCount     int64   `json:"record_count"`         // 记录数量
    ReadQPS         float64 `json:"read_qps"`             // 读取QPS
    WriteQPS        float64 `json:"write_qps"`            // 写入QPS
    AvgQueryTime    float64 `json:"avg_query_time_ms"`    // 平均查询时间(ms)
    SlowQueryCount  int     `json:"slow_query_count"`     // 慢查询数量
    ConnectionPool  int     `json:"connection_pool_active"` // 活跃连接数
}

// Conversations表监控指标
type ConversationTableMetrics struct {
    TableName       string  `json:"table_name"`           // 表名
    TableSize       int64   `json:"table_size_mb"`        // 表大小(MB)
    RecordCount     int64   `json:"record_count"`         // 记录数量
    ActiveUsers     int64   `json:"active_users"`         // 活跃用户数
    ReadQPS         float64 `json:"read_qps"`             // 读取QPS
    WriteQPS        float64 `json:"write_qps"`            // 写入QPS
    AvgQueryTime    float64 `json:"avg_query_time_ms"`    // 平均查询时间(ms)
    SlowQueryCount  int     `json:"slow_query_count"`     // 慢查询数量
}

// 系统整体监控指标
type SystemMetrics struct {
    MessageTable      MessageTableMetrics      `json:"message_table"`
    ConversationTable ConversationTableMetrics `json:"conversation_table"`
    TotalUsers        int64                    `json:"total_users"`
    TotalConversations int64                   `json:"total_conversations"`
    TotalMessages     int64                    `json:"total_messages"`
}
```

##### 2. 告警规则

```yaml
# Messages表性能告警
- name: message_table_performance
  rules:
    - alert: MessageSlowQuery
      expr: message_table.avg_query_time_ms > 100
      for: 5m
      labels:
        severity: warning
        table: messages
      
    - alert: MessageHighWriteQPS
      expr: message_table.write_qps > 2000
      for: 2m
      labels:
        severity: critical
        table: messages
      
    - alert: MessageTableSizeWarning
      expr: message_table.table_size_mb > 10240  # 10GB
      for: 1m
      labels:
        severity: warning
        table: messages

# Messages表容量告警      
- name: message_table_capacity
  rules:
    - alert: MessageRecordCountWarning
      expr: message_table.record_count > 5000000  # 500万条
      for: 1m
      labels:
        severity: warning
        table: messages

# Conversations表性能告警
- name: conversation_table_performance
  rules:
    - alert: ConversationSlowQuery
      expr: conversation_table.avg_query_time_ms > 50
      for: 5m
      labels:
        severity: warning
        table: conversations
      
    - alert: ConversationHighReadQPS
      expr: conversation_table.read_qps > 5000
      for: 2m
      labels:
        severity: warning
        table: conversations
      
    - alert: ConversationTableSizeWarning
      expr: conversation_table.table_size_mb > 2048  # 2GB
      for: 1m
      labels:
        severity: warning
        table: conversations

# Conversations表容量告警      
- name: conversation_table_capacity
  rules:
    - alert: ConversationRecordCountWarning
      expr: conversation_table.record_count > 1000000  # 100万条
      for: 1m
      labels:
        severity: warning
        table: conversations
        
    - alert: ActiveUserCountHigh
      expr: conversation_table.active_users > 50000
      for: 5m
      labels:
        severity: info
        table: conversations
```

## 错误码定义

```go
const (
    // 帖子相关错误
    ErrPostNotFound     = 40001 // 帖子不存在
    ErrPostStatusInvalid = 40002 // 帖子状态无效
    ErrPostPermissionDenied = 40003 // 无权限操作帖子
    
    // 会话相关错误
    ErrConversationNotFound = 40101 // 会话不存在
    ErrConversationAccessDenied = 40102 // 无权限访问会话
    ErrInvalidReadTime = 40103 // 无效的阅读时间
    
    // 消息相关错误
    ErrMessageContentEmpty = 40201 // 消息内容为空
    ErrMessageTooLong     = 40202 // 消息内容过长
    ErrImageUploadFailed  = 40203 // 图片上传失败
    ErrClientMsgIDEmpty   = 40204 // 客户端消息ID为空
    ErrMessageDuplicate   = 40205 // 消息重复（基于client_msg_id检测）
    
    // 智能回复相关错误
    ErrTemplateNotFound   = 40301 // 模板不存在
    ErrTemplateContentEmpty = 40302 // 模板内容为空
    
    // 管理端相关错误
    ErrApplicationNotFound   = 40402 // 申请记录不存在
    ErrApplicationAlreadyReviewed = 40403 // 申请已被审核
)
```

## API设计

### RESTful API规范

#### 帖子相关API

```
GET    /web/v1/posts/list                    # 获取帖子列表
GET    /web/v1/posts/detail                  # 获取帖子详情（参数：id）
POST   /web/v1/posts/add                     # 创建帖子
POST   /web/v1/posts/edit                    # 更新帖子状态（参数：id, status）
POST   /web/v1/posts/delete                  # 删除帖子（参数：id）
GET    /web/v1/posts/my                      # 获取我的帖子
```

#### 会话相关API

```
GET    /web/v1/conversations/list            # 获取会话列表
GET    /web/v1/conversations/detail          # 获取会话详情（参数：id）
POST   /web/v1/conversations/add             # 创建会话
```

#### 消息相关API

```
GET    /web/v1/conversations/messages/list   # 获取消息列表（参数：conversation_id）
POST   /web/v1/conversations/messages/add    # 发送消息（参数：conversation_id）
POST   /web/v1/conversations/read            # 标记会话为已读（参数：conversation_id）
```

#### 智能回复相关API

```
GET    /web/v1/smart_reply_template/detail          # 获取智能回复模板（包含开关和内容）
POST   /web/v1/smart_reply_template/edit          # 更新智能回复模板内容
POST   /web/v1/smart_reply_template/toggle            # 切换智能回复开关
```

#### 商家申请相关API

```
GET    /web/v1/merchant_application/status   # 获取我的申请状态
POST   /web/v1/merchant_application/add      # 提交商家申请
```

#### 管理端API

##### 求购管理API

```
GET    /admin/v1/posts/list                  # 获取求购帖子列表
GET    /admin/v1/posts/detail                # 获取求购帖子详情
POST   /admin/v1/posts/edit                  # 更新帖子状态
```

##### 会话管理API

```
GET    /admin/v1/conversations/list          # 获取会话列表
GET    /admin/v1/conversations/detail        # 获取会话详情
```

##### 消息管理API

```
GET    /admin/v1/conversations/messages/list # 获取消息列表
```

##### 商家管理API

```
GET    /admin/v1/merchant_applications/list    # 获取商家申请列表
GET    /admin/v1/merchant_applications/detail  # 获取商家申请详情
POST   /admin/v1/merchant_applications/review  # 审核商家申请
```

## 错误处理

### 错误处理策略

1. **参数验证错误**: 返回400状态码和具体错误信息
2. **权限验证错误**: 返回403状态码
3. **资源不存在错误**: 返回404状态码
4. **业务逻辑错误**: 返回422状态码和业务错误码
5. **系统错误**: 返回500状态码，记录详细日志

### 系统监控错误处理

1. **消息发送失败处理**: 
   - 记录详细错误日志，包含会话ID、发送者ID、消息类型等信息
   - 标记消息状态为发送失败
   - 返回适当的错误码和提示信息，允许用户重试
   - 定期统计失败率，超过阈值时触发告警

2. **推送服务异常处理**:
   - 检测到推送服务异常时，自动切换到备用推送通道
   - 记录服务异常日志，包含异常时间、类型和影响范围
   - 实现推送任务重试机制，最多重试3次
   - 持续监控推送服务状态，服务恢复后自动切回主通道

3. **数据库连接异常处理**:
   - 实现数据库连接池健康检查机制
   - 定期检测连接状态，自动重连失效连接
   - 当数据库连接异常时，返回友好的错误提示给用户
   - 记录详细的数据库异常日志，包含SQL语句、错误码和堆栈信息
   - 实现数据库操作重试机制，最多重试3次

4. **系统负载过高处理**:
   - 实现请求限流机制，当系统负载超过阈值时自动限制并发请求数
   - 使用令牌桶算法控制API访问频率
   - 为不同类型的API设置不同的限流策略
   - 当触发限流时，返回429状态码和友好提示
   - 记录限流事件日志，包含触发时间、原因和影响范围

5. **消息存储清理机制**:
   - 实现定时任务，定期检查消息存储容量
   - 当存储容量达到预设阈值时，自动清理过期消息
   - 优先清理时间最早的消息，保留最近N天的消息记录
   - 清理前自动备份重要消息数据
   - 记录清理操作日志，包含清理时间、数量和释放空间常处理**:
   - 实现数据库连接池健康检查
   - 连接异常时返回友好的错误提示
   - 记录详细的连接异常日志，包含SQL语句、错误码等信息
   - 实现自动重连机制，避免服务长时间不可用

4. **系统负载过高处理**:
   - 实现请求限流机制，当系统负载超过阈值时自动启用
   - 对非关键请求进行降级处理
   - 返回适当的错误码和提示信息，建议用户稍后重试
   - 记录系统负载情况，为容量规划提供依据

5. **消息存储达到阈值处理**:
   - 定期检查消息存储使用情况
   - 达到预警阈值时，自动清理过期消息（如90天前的消息）
   - 实现消息归档机制，将旧消息转移到低成本存储
   - 记录清理和归档操作日志，确保数据可追溯

### 幂等性和重试机制

#### 消息发送幂等性
基于`client_msg_id`的幂等性设计确保消息不会重复：

```go
// 幂等性检查流程
func (s *MessageService) SendMessage(req *SendMessageRequest) error {
    // 1. 检查client_msg_id是否已存在
    existing, err := s.repo.GetMessageByClientID(req.ClientMsgID)
    if err == nil && existing != nil {
        // 消息已存在，返回成功（幂等）
        return nil
    }
    
    // 2. 创建新消息
    return s.createNewMessage(req)
}
```

#### HTTP请求重试策略
- **GET请求**: 自动重试，最多3次，指数退避
- **POST请求**: 基于幂等性设计，支持安全重试
- **文件上传**: 支持断点续传和重试机制
- **超时处理**: 设置合理的请求超时时间，避免长时间等待

#### Kafka消息重试
- **生产者重试**: 自动重试失败的消息发送，最多重试3次
- **消费者重试**: 处理失败的消息进入重试队列，延迟重试
- **死信队列**: 多次重试失败的消息进入死信队列，人工处理
- **监控告警**: 重试次数过多时触发告警，及时发现系统问题


## 安全考虑

### 认证授权

- **JWT Token认证**: 使用现有的用户认证系统
- **权限控制**: 确保用户只能操作自己的数据
- **角色验证**: 区分普通用户、商家和管理员的操作权限
- **管理端权限**: 管理员接口需要验证管理员身份，防止越权访问
- **操作审计**: 记录所有管理员操作，确保可追溯性

### 数据安全

- **输入验证**: 严格验证所有用户输入
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 对用户输入进行HTML转义
- **文件上传安全**: 限制文件类型和大小

### 接口安全

- **请求频率限制**: 防止恶意刷接口
- **敏感信息脱敏**: 日志中不记录敏感信息
- **HTTPS传输**: 确保数据传输安全

## 性能优化

### 数据库优化

- **索引优化**: 为常用查询字段添加索引
- **查询优化**: 避免N+1查询问题
- **分页优化**: 使用游标分页提升大数据量查询性能

### 缓存策略

- **会话列表缓存**: 缓存用户的会话列表
- **未读计数缓存**: 使用Redis缓存未读消息数量
  - 缓存键格式: `unread_count:{user_id}:{conversation_id}`
  - 缓存键格式: `total_unread_count:{user_id}` (用户总未读数)
  - 过期时间: 30分钟，支持主动更新和删除
- **已读状态缓存**: 缓存用户的最后阅读时间
  - 缓存键格式: `last_read_time:{user_id}:{conversation_id}`
  - 用于快速计算未读消息数量，减少数据库查询
- **热点数据缓存**: 缓存热门帖子信息

### 代码优化

- **连接池管理**: 合理配置数据库连接池
- **异步处理**: 对非关键路径使用异步处理
- **资源释放**: 及时释放数据库连接和文件句柄


## 测试策略

### 单元测试

- **Service层测试**: 测试业务逻辑的正确性
- **Repository层测试**: 测试数据访问的准确性
- **工具函数测试**: 测试辅助函数的功能

### 集成测试

- **API接口测试**: 测试完整的请求响应流程
- **数据库集成测试**: 测试数据持久化的正确性
- **第三方服务集成测试**: 测试文件上传等外部依赖

### 测试数据准备

```go
// 测试用户数据
var TestUsers = []User{
    {ID: "user_001", Name: "测试用户1", UserType: UserTypeNormal},
    {ID: "merchant_001", Name: "测试商家1", UserType: UserTypeMerchant},
}

// 测试帖子数据
var TestPosts = []Post{
    {
        ID: "post_001",
        MerchantID: "merchant_001",
        Title: "收购皮卡丘卡片",
        Description: "高价收购初版皮卡丘卡片",
        Price: 10000, // 100.00元 = 10000分
        Status: PostStatusActive,
    },
}
```

### 性能测试

- **并发用户测试**: 模拟多用户同时访问
- **消息发送压力测试**: 测试消息处理能力
- **数据库查询性能测试**: 优化慢查询
